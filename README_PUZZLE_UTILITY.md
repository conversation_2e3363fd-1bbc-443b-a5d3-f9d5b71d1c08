# Flow Free Puzzle Input Utility

A comprehensive utility for creating, validating, and solving Flow Free puzzles from various input formats, especially designed to make it easy to translate puzzles from pictures.

## 🎯 Overview

This utility provides multiple ways to input Flow Free puzzles and convert them to the format expected by the FlowFreeSolver. It's particularly useful for translating puzzles from pictures, mobile games, or puzzle books into a solvable format.

## 📁 Files Created

- **`puzzle_input_utility.py`** - Main utility with input methods and validation
- **`puzzle_collection.py`** - Pre-made puzzle collection with various difficulties  
- **`test_puzzle_input.py`** - Comprehensive test suite demonstrating all features
- **`demo_puzzle_utility.py`** - Simple demonstration script
- **`README_PUZZLE_UTILITY.md`** - This documentation

## 🚀 Quick Start

```python
from puzzle_input_utility import PuzzleInputUtility

utility = PuzzleInputUtility()

# Method 1: Grid format (best for picture translation)
puzzle = utility.create_from_grid([
    ['1', '.', '.', '2'],
    ['.', '.', '.', '.'],
    ['.', '.', '.', '.'],
    ['2', '.', '.', '1']
])

# Preview the puzzle
utility.print_puzzle_preview(puzzle)

# Solve it
solution = utility.solve_puzzle(puzzle)
```

## 🎨 Input Methods

### 1. Grid Format (Recommended for Pictures)
Best for translating visual puzzles from pictures:

```python
puzzle = utility.create_from_grid([
    ['R', '.', '.', 'B'],  # R = Red dots, B = Blue dots
    ['.', 'G', '.', '.'],  # G = Green dots
    ['.', '.', 'G', '.'],
    ['B', '.', '.', 'R']
])
```

### 2. Coordinate Format (Precise Control)
Best when you know exact positions:

```python
puzzle = utility.create_from_coordinates(
    rows=4, cols=4,
    endpoints={
        1: [(0, 0), (3, 3)],  # Symbol 1: top-left to bottom-right
        2: [(0, 3), (3, 0)],  # Symbol 2: top-right to bottom-left
        3: [(1, 1), (2, 2)]   # Symbol 3: inner diagonal
    }
)
```

### 3. String Format (Copy-Paste Friendly)
Best for copying from text sources:

```python
puzzle_str = """
1 . . 2
. . . .
. . . .
2 . . 1
"""
puzzle = utility.create_from_string(puzzle_str)
```

## 🖼️ Picture Translation Workflow

When you have a Flow Free puzzle in picture format:

1. **Identify Grid Size**: Count rows and columns
2. **Locate Endpoints**: Find pairs of colored dots/circles/numbers
3. **Choose Input Method**: Grid format is usually best
4. **Create Puzzle**: Use the utility to create the puzzle
5. **Validate & Solve**: Check validity and solve

### Example Picture Translation

```python
# Picture shows: 5x5 grid with red circles at corners, blue on edges
puzzle = utility.create_from_grid([
    ['R', '.', '.', '.', 'B'],
    ['.', '.', '.', '.', '.'],
    ['.', 'G', '.', 'G', '.'],  # Green circles in middle row
    ['.', '.', '.', '.', '.'],
    ['B', '.', '.', '.', 'R']
])

# Validate before solving
is_valid, errors = utility.validate_puzzle(puzzle)
if is_valid:
    solution = utility.solve_puzzle(puzzle)
```

## 📚 Pre-Made Puzzle Collection

```python
from puzzle_collection import PuzzleCollection

collection = PuzzleCollection()

# Browse available puzzles
collection.print_puzzle_catalog()

# Solve a specific puzzle
solution = collection.solve_puzzle("easy_3x3_cross")

# Get puzzles by difficulty
easy_puzzles = collection.get_puzzles_by_difficulty("easy")

# Solve all puzzles of a difficulty
collection.solve_all_puzzles("easy")
```

### Available Puzzle Categories

- **Easy**: Simple 2x2 to 4x4 puzzles with few pairs
- **Medium**: 4x4 to 5x5 puzzles with moderate complexity
- **Hard**: 5x5+ puzzles with many pairs and complex patterns
- **Special**: Unique formats (rectangular, edge-focused, etc.)

## ⚡ Batch Processing

Process multiple puzzles at once:

```python
from puzzle_input_utility import BatchPuzzleProcessor

processor = BatchPuzzleProcessor()

# Process multiple grid representations
puzzle_grids = [
    [['1', '2'], ['2', '1']],  # 2x2 puzzle
    [['1', '.', '1']],         # 1x3 puzzle
    # ... more puzzles
]

results = processor.create_puzzles_from_grids(puzzle_grids, solve=True)
processor.print_batch_summary()
```

## 🔧 Features

### Validation & Error Checking
- Ensures each symbol appears exactly twice
- Validates coordinates are within bounds
- Checks for basic solvability constraints
- Provides detailed error messages

### Quality Metrics
- Path length analysis
- Reachability checking
- Solution quality scoring
- Multiple solution discovery

### Export/Import
- Save puzzle collections to JSON
- Load puzzles from files
- Export individual puzzles
- Batch processing capabilities

## 🎮 Usage Examples

### Simple Translation
```python
# You see a 3x3 puzzle with numbered circles
utility = PuzzleInputUtility()
puzzle = utility.create_from_grid([
    ['1', '.', '2'],
    ['.', '.', '.'],
    ['2', '.', '1']
])
solution = utility.solve_puzzle(puzzle)
```

### Complex Picture Puzzle
```python
# 6x6 puzzle from a mobile game screenshot
puzzle = utility.create_from_coordinates(
    rows=6, cols=6,
    endpoints={
        'red': [(0, 0), (5, 5)],
        'blue': [(0, 5), (5, 0)],
        'green': [(2, 1), (3, 4)],
        'yellow': [(1, 2), (4, 3)]
    }
)
```

### Validation Before Solving
```python
puzzle = utility.create_from_grid(grid_data)
is_valid, errors = utility.validate_puzzle(puzzle)

if is_valid:
    print("✅ Puzzle is valid!")
    solution = utility.solve_puzzle(puzzle)
else:
    print("❌ Puzzle has errors:")
    for error in errors:
        print(f"  - {error}")
```

## 🚨 Common Mistakes to Avoid

1. **Coordinate Mix-up**: Remember coordinates are (row, column), starting from (0,0)
2. **Missing Pairs**: Each symbol must appear exactly twice
3. **Out of Bounds**: Ensure all coordinates fit within the grid
4. **Inconsistent Symbols**: Use the same symbol for both endpoints of a pair

## 🎯 Tips for Success

1. **Start Simple**: Begin with small puzzles to understand the format
2. **Use Preview**: Always preview puzzles before solving
3. **Validate First**: Check puzzle validity before attempting to solve
4. **Grid Method**: Use grid format for most picture translations
5. **Test Incrementally**: Build complex puzzles step by step

## 🔄 Running the Examples

```bash
# Run the comprehensive test suite
python test_puzzle_input.py

# Run the simple demonstration
python demo_puzzle_utility.py

# Explore the puzzle collection
python puzzle_collection.py

# Try the main utility
python puzzle_input_utility.py
```

## 📊 Success Metrics

The utility successfully:
- ✅ Provides 3 different input methods for maximum flexibility
- ✅ Includes comprehensive validation and error checking
- ✅ Offers a collection of 12+ pre-made puzzles
- ✅ Supports batch processing for multiple puzzles
- ✅ Provides detailed documentation and examples
- ✅ Integrates seamlessly with the existing FlowFreeSolver

## 🎉 Conclusion

This utility makes it significantly easier to input Flow Free puzzles from various sources, especially pictures. Whether you're translating puzzles from mobile games, puzzle books, or creating your own challenges, the utility provides the tools you need to quickly convert visual puzzles into a solvable format.

The combination of multiple input methods, validation, pre-made collections, and batch processing makes this a comprehensive solution for Flow Free puzzle management and solving.
