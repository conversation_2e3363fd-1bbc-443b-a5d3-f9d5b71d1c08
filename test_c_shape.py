#!/usr/bin/env python3
"""
Test case specifically designed to trigger C-shape detection.
This test creates a scenario where a C-shape pattern would be formed.
"""

from main import FlowFreeSolver

def test_c_shape_detection():
    """Test a puzzle that would create a C-shape if not properly detected."""
    print("=== C-Shape Detection Test ===")
    
    # Create a 4x3 puzzle that could lead to C-shape formation
    # The path from (0,1) to (3,1) could potentially form a C-shape
    # if it goes: (0,1) → (0,0) → (1,0) → (2,0) → (2,1) → (3,1)
    pairs = {
        1: ((0, 1), (3, 1)),  # This could form a C-shape
        2: ((1, 2), (2, 2))   # Simple connection to fill some space
    }
    
    # Create and display the puzzle
    solver = FlowFreeSolver(4, 3, pairs)
    solver.print_current_grid("C-Shape Test Puzzle")
    
    # Solve the puzzle
    print("🚀 Starting solver...")
    if solver.solve():
        solver.print_solution()
        return True
    else:
        print("❌ No solution found (this might be expected if C-shape detection works)")
        solver.print_current_grid("Final State (No Solution)")
        return False

def test_exact_c_shape():
    """Test the exact C-shape pattern mentioned: (0,1) → (0,0) → (1,0) → (2,0) → (2,1)."""
    print("\n=== Exact C-Shape Test ===")

    # Create a 3x3 puzzle that would force the exact C-shape pattern
    # Path from (0,1) to (2,1) could go: (0,1) → (0,0) → (1,0) → (2,0) → (2,1)
    pairs = {
        1: ((0, 1), (2, 1)),  # This should create the C-shape
        2: ((1, 2), (1, 1))   # Block the direct path to force the C
    }

    solver = FlowFreeSolver(3, 3, pairs)
    solver.print_current_grid("Exact C-Shape Test")

    print("🚀 Starting solver...")
    if solver.solve():
        solver.print_solution()
        return True
    else:
        print("❌ No solution found")
        solver.print_current_grid("Final State (No Solution)")
        return False

def test_pocket_pattern():
    """Test patterns that create pockets with multiple trapped cells."""
    print("\n=== Pocket Pattern Test ===")

    # Create a 4x4 puzzle that could create a pocket with 2 trapped cells
    # This should trigger the enhanced C-shape detection
    pairs = {
        1: ((0, 0), (3, 3)),  # Long diagonal that might create pockets
        2: ((0, 3), (3, 0))   # Cross pattern to force complex paths
    }

    solver = FlowFreeSolver(4, 4, pairs)
    solver.print_current_grid("Pocket Pattern Test")

    print("🚀 Starting solver...")
    if solver.solve():
        solver.print_solution()
        return True
    else:
        print("❌ No solution found (expected - should detect pocket patterns)")
        solver.print_current_grid("Final State (No Solution)")
        return False

def test_bay_pattern():
    """Test a pattern that creates a bay-like shape."""
    print("\n=== Bay Pattern Test ===")

    # Create a 3x4 puzzle that could create a bay pattern
    pairs = {
        1: ((0, 0), (2, 3)),  # Path that might wrap around
        2: ((1, 1), (1, 2))   # Small connection in the middle
    }

    solver = FlowFreeSolver(3, 4, pairs)
    solver.print_current_grid("Bay Pattern Test")

    print("🚀 Starting solver...")
    if solver.solve():
        solver.print_solution()
        return True
    else:
        print("❌ No solution found")
        solver.print_current_grid("Final State (No Solution)")
        return False

def test_simple_c_shape():
    """Test a simpler case that definitely creates a C-shape."""
    print("\n=== Simple C-Shape Test ===")

    # Create a 3x4 puzzle designed to force a C-shape
    pairs = {
        1: ((0, 0), (2, 2))  # Long diagonal that might create C-shape
    }

    solver = FlowFreeSolver(3, 4, pairs)
    solver.print_current_grid("Simple C-Shape Test")

    print("🚀 Starting solver...")
    if solver.solve():
        solver.print_solution()
        return True
    else:
        print("❌ No solution found")
        solver.print_current_grid("Final State (No Solution)")
        return False

if __name__ == "__main__":
    print("Running C-shape detection tests...\n")
    
    # Run the tests
    test1_success = test_c_shape_detection()
    test2_success = test_exact_c_shape()
    test3_success = test_simple_c_shape()

    # Summary
    print("\n" + "="*50)
    print("Test Results:")
    print(f"C-Shape Detection Test: {'✅ PASSED' if test1_success else '❌ FAILED'}")
    print(f"Exact C-Shape Test: {'✅ PASSED' if test2_success else '❌ FAILED'}")
    print(f"Simple C-Shape Test: {'✅ PASSED' if test3_success else '❌ FAILED'}")

    if test1_success or test2_success or test3_success:
        print("\n🎉 At least one test found a solution!")
    else:
        print("\n⚠️  No solutions found - C-shape detection may be working correctly!")
