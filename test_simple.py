#!/usr/bin/env python3
"""
Simple 1x3 Flow Free puzzle test case.
This is a basic test to verify the solver works correctly.
"""

from main import FlowFreeSolver

def test_simple_1x3():
    """Test a simple 1x3 puzzle that should solve quickly."""
    print("=== Simple 1x3 Puzzle Test ===")
    
    # Define the puzzle: connect 1 to 1 across a 1x3 grid
    pairs = {
        1: ((0, 0), (0, 2))  # Connect (0,0) to (0,2)
    }
    
    # Create and display the puzzle
    solver = FlowFreeSolver(1, 3, pairs)
    solver.print_current_grid("Simple 1x3 Puzzle")
    
    # Solve the puzzle
    print("🚀 Starting solver...")
    if solver.solve():
        solver.print_solution()
        return True
    else:
        print("❌ No solution found")
        solver.print_current_grid("Final State (No Solution)")
        return False

if __name__ == "__main__":
    success = test_simple_1x3()
    if success:
        print("\n✅ Test passed!")
    else:
        print("\n❌ Test failed!")
