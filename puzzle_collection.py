#!/usr/bin/env python3
"""
Flow Free Puzzle Collection

This file contains a collection of pre-made Flow Free puzzles of various
difficulties and sizes. These can be used for testing the solver or as
examples for creating your own puzzles.

Usage:
    from puzzle_collection import PuzzleCollection
    
    collection = PuzzleCollection()
    
    # Get a specific puzzle
    puzzle = collection.get_puzzle("easy_3x3")
    
    # Solve all puzzles
    collection.solve_all_puzzles()
    
    # Get puzzles by difficulty
    easy_puzzles = collection.get_puzzles_by_difficulty("easy")
"""

from puzzle_input_utility import PuzzleInputUtility
from typing import Dict, List, Optional


class PuzzleCollection:
    """Collection of pre-made Flow Free puzzles."""
    
    def __init__(self):
        """Initialize the puzzle collection."""
        self.utility = PuzzleInputUtility()
        self.puzzles = {}
        self._create_puzzle_collection()
    
    def _create_puzzle_collection(self):
        """Create the collection of puzzles."""
        
        # ===== EASY PUZZLES (3x3 to 4x4) =====
        
        # Simple 1x3 (from original test)
        self.puzzles["simple_1x3"] = {
            "puzzle": self.utility.create_from_coordinates(
                rows=1, cols=3,
                endpoints={1: [(0, 0), (0, 2)]}
            ),
            "difficulty": "easy",
            "description": "Simple 1x3 line connection"
        }
        
        # Easy 3x3 cross pattern
        self.puzzles["easy_3x3_cross"] = {
            "puzzle": self.utility.create_from_grid([
                ['1', '.', '2'],
                ['.', '.', '.'],
                ['2', '.', '1']
            ]),
            "difficulty": "easy",
            "description": "3x3 cross pattern with diagonal connections"
        }
        
        # Easy 3x3 adjacent
        self.puzzles["easy_3x3_adjacent"] = {
            "puzzle": self.utility.create_from_grid([
                ['1', '1', '.'],
                ['.', '.', '.'],
                ['.', '2', '2']
            ]),
            "difficulty": "easy",
            "description": "3x3 with adjacent endpoints"
        }
        
        # Easy 4x4 corners
        self.puzzles["easy_4x4_corners"] = {
            "puzzle": self.utility.create_from_grid([
                ['1', '.', '.', '2'],
                ['.', '.', '.', '.'],
                ['.', '.', '.', '.'],
                ['2', '.', '.', '1']
            ]),
            "difficulty": "easy",
            "description": "4x4 corner-to-corner connections"
        }
        
        # ===== MEDIUM PUZZLES (4x4 to 5x5) =====
        
        # Medium 4x4 multi-pair
        self.puzzles["medium_4x4_multi"] = {
            "puzzle": self.utility.create_from_grid([
                ['1', '.', '2', '.'],
                ['.', '3', '.', '4'],
                ['4', '.', '3', '.'],
                ['.', '2', '.', '1']
            ]),
            "difficulty": "medium",
            "description": "4x4 with 4 pairs to connect"
        }
        
        # Medium 5x5 (from original test)
        self.puzzles["medium_5x5_complex"] = {
            "puzzle": self.utility.create_from_coordinates(
                rows=5, cols=5,
                endpoints={
                    1: [(0, 0), (4, 1)],
                    2: [(0, 2), (3, 1)],
                    3: [(1, 2), (4, 2)],
                    4: [(0, 4), (3, 3)],
                    5: [(1, 4), (4, 3)]
                }
            ),
            "difficulty": "medium",
            "description": "5x5 complex puzzle with 5 pairs"
        }
        
        # Medium 5x5 symmetric
        self.puzzles["medium_5x5_symmetric"] = {
            "puzzle": self.utility.create_from_grid([
                ['1', '.', '2', '.', '3'],
                ['.', '.', '.', '.', '.'],
                ['4', '.', '5', '.', '4'],
                ['.', '.', '.', '.', '.'],
                ['3', '.', '2', '5', '1']
            ]),
            "difficulty": "medium",
            "description": "5x5 symmetric pattern"
        }
        
        # ===== HARD PUZZLES (5x5+) =====
        
        # Hard 5x5 dense
        self.puzzles["hard_5x5_dense"] = {
            "puzzle": self.utility.create_from_coordinates(
                rows=5, cols=5,
                endpoints={
                    1: [(0, 0), (4, 4)],  # Diagonal
                    2: [(0, 4), (4, 0)],  # Other diagonal
                    3: [(0, 1), (3, 2)],  # Complex path
                    4: [(0, 3), (2, 0)],  # Another complex path
                    5: [(1, 0), (2, 4)],  # Cross pattern
                    6: [(1, 1), (3, 3)],  # Inner diagonal
                    7: [(2, 1), (4, 3)]   # Final pair
                }
            ),
            "difficulty": "hard",
            "description": "5x5 dense puzzle with 7 pairs"
        }
        
        # Hard 6x6 challenge
        self.puzzles["hard_6x6_challenge"] = {
            "puzzle": self.utility.create_from_grid([
                ['1', '.', '2', '3', '.', '4'],
                ['.', '.', '.', '.', '.', '.'],
                ['5', '.', '6', '6', '.', '5'],
                ['.', '.', '.', '.', '.', '.'],
                ['7', '.', '8', '8', '.', '7'],
                ['4', '.', '3', '2', '.', '1']
            ]),
            "difficulty": "hard",
            "description": "6x6 challenge with 8 pairs"
        }
        
        # ===== SPECIAL PUZZLES =====
        
        # Rectangular puzzle
        self.puzzles["special_3x5_rect"] = {
            "puzzle": self.utility.create_from_grid([
                ['1', '.', '2', '.', '3'],
                ['.', '.', '.', '.', '.'],
                ['3', '.', '2', '.', '1']
            ]),
            "difficulty": "medium",
            "description": "3x5 rectangular puzzle"
        }
        
        # Minimal puzzle (2x2)
        self.puzzles["special_2x2_minimal"] = {
            "puzzle": self.utility.create_from_grid([
                ['1', '2'],
                ['2', '1']
            ]),
            "difficulty": "easy",
            "description": "2x2 minimal puzzle"
        }
        
        # Edge-heavy puzzle
        self.puzzles["special_4x4_edges"] = {
            "puzzle": self.utility.create_from_coordinates(
                rows=4, cols=4,
                endpoints={
                    1: [(0, 0), (0, 3)],  # Top edge
                    2: [(3, 0), (3, 3)],  # Bottom edge
                    3: [(0, 1), (3, 1)],  # Left side
                    4: [(0, 2), (3, 2)]   # Right side
                }
            ),
            "difficulty": "medium",
            "description": "4x4 puzzle with edge-focused connections"
        }
    
    def get_puzzle(self, name: str) -> Optional[Dict]:
        """
        Get a specific puzzle by name.
        
        Args:
            name: Name of the puzzle
            
        Returns:
            Puzzle dictionary or None if not found
        """
        if name in self.puzzles:
            return self.puzzles[name]["puzzle"]
        return None
    
    def get_puzzle_info(self, name: str) -> Optional[Dict]:
        """
        Get full information about a puzzle including metadata.
        
        Args:
            name: Name of the puzzle
            
        Returns:
            Full puzzle info dictionary or None if not found
        """
        return self.puzzles.get(name)
    
    def list_puzzles(self) -> List[str]:
        """Get a list of all puzzle names."""
        return list(self.puzzles.keys())
    
    def get_puzzles_by_difficulty(self, difficulty: str) -> Dict[str, Dict]:
        """
        Get all puzzles of a specific difficulty.
        
        Args:
            difficulty: "easy", "medium", "hard", or "special"
            
        Returns:
            Dictionary of puzzle_name -> puzzle_info
        """
        return {
            name: info for name, info in self.puzzles.items()
            if info["difficulty"] == difficulty
        }
    
    def print_puzzle_catalog(self):
        """Print a catalog of all available puzzles."""
        print("╔══════════════════════════════════════════════════════════════════════════════╗")
        print("║                           FLOW FREE PUZZLE CATALOG                          ║")
        print("╚══════════════════════════════════════════════════════════════════════════════╝")
        
        difficulties = ["easy", "medium", "hard", "special"]
        
        for difficulty in difficulties:
            puzzles = self.get_puzzles_by_difficulty(difficulty)
            if puzzles:
                print(f"\n🎯 {difficulty.upper()} PUZZLES:")
                print("-" * 50)
                
                for name, info in puzzles.items():
                    puzzle = info["puzzle"]
                    size = f"{puzzle['rows']}x{puzzle['cols']}"
                    pairs = len(puzzle['pairs'])
                    description = info["description"]
                    
                    print(f"  📋 {name}")
                    print(f"     Size: {size} | Pairs: {pairs} | {description}")
        
        print(f"\n📊 Total puzzles: {len(self.puzzles)}")
        print("\nUsage:")
        print("  collection = PuzzleCollection()")
        print("  puzzle = collection.get_puzzle('easy_3x3_cross')")
        print("  collection.solve_puzzle('medium_5x5_complex')")
    
    def solve_puzzle(self, name: str, verbose: bool = True) -> Optional[List[List[int]]]:
        """
        Solve a specific puzzle by name.
        
        Args:
            name: Name of the puzzle to solve
            verbose: Whether to print solving progress
            
        Returns:
            Solution grid or None if puzzle not found or unsolvable
        """
        if name not in self.puzzles:
            if verbose:
                print(f"❌ Puzzle '{name}' not found")
            return None
        
        puzzle_info = self.puzzles[name]
        puzzle = puzzle_info["puzzle"]
        
        if verbose:
            print(f"\n🎯 Solving: {name}")
            print(f"   Difficulty: {puzzle_info['difficulty']}")
            print(f"   Description: {puzzle_info['description']}")
        
        return self.utility.solve_puzzle(puzzle, verbose)
    
    def solve_all_puzzles(self, difficulty_filter: Optional[str] = None):
        """
        Solve all puzzles in the collection.
        
        Args:
            difficulty_filter: Optional filter by difficulty ("easy", "medium", "hard", "special")
        """
        puzzles_to_solve = self.puzzles
        if difficulty_filter:
            puzzles_to_solve = {
                name: info for name, info in self.puzzles.items()
                if info["difficulty"] == difficulty_filter
            }
        
        print(f"🚀 Solving {len(puzzles_to_solve)} puzzles...")
        if difficulty_filter:
            print(f"   Filter: {difficulty_filter} difficulty")
        
        results = {}
        for name, info in puzzles_to_solve.items():
            print(f"\n{'='*60}")
            solution = self.solve_puzzle(name, verbose=True)
            results[name] = solution is not None
        
        # Summary
        print(f"\n{'='*60}")
        print("SOLVING SUMMARY")
        print(f"{'='*60}")
        
        solved = sum(1 for success in results.values() if success)
        total = len(results)
        
        for name, success in results.items():
            status = "✅ SOLVED" if success else "❌ FAILED"
            difficulty = self.puzzles[name]["difficulty"]
            print(f"  {status}: {name} ({difficulty})")
        
        print(f"\nOverall: {solved}/{total} puzzles solved")
        
        if solved == total:
            print("🎉 All puzzles solved successfully!")
        else:
            print("⚠️  Some puzzles couldn't be solved.")
    
    def export_puzzle(self, name: str, filename: str):
        """Export a specific puzzle to a JSON file."""
        if name not in self.puzzles:
            print(f"❌ Puzzle '{name}' not found")
            return
        
        puzzle_data = {
            "name": name,
            "puzzle": self.puzzles[name]["puzzle"],
            "difficulty": self.puzzles[name]["difficulty"],
            "description": self.puzzles[name]["description"]
        }
        
        import json
        with open(filename, 'w') as f:
            json.dump(puzzle_data, f, indent=2)
        
        print(f"✅ Exported puzzle '{name}' to {filename}")
    
    def get_random_puzzle(self, difficulty: Optional[str] = None) -> tuple:
        """
        Get a random puzzle from the collection.
        
        Args:
            difficulty: Optional difficulty filter
            
        Returns:
            Tuple of (puzzle_name, puzzle_dict)
        """
        import random
        
        if difficulty:
            candidates = self.get_puzzles_by_difficulty(difficulty)
            if not candidates:
                return None, None
            name = random.choice(list(candidates.keys()))
        else:
            name = random.choice(list(self.puzzles.keys()))
        
        return name, self.puzzles[name]["puzzle"]


def demo_puzzle_collection():
    """Demonstrate the puzzle collection functionality."""
    print("🎮 PUZZLE COLLECTION DEMO")
    print("="*50)
    
    collection = PuzzleCollection()
    
    # Show catalog
    collection.print_puzzle_catalog()
    
    # Solve a few example puzzles
    print("\n🎯 SOLVING SAMPLE PUZZLES:")
    print("="*50)
    
    # Easy puzzle
    print("\n--- Easy Puzzle ---")
    collection.solve_puzzle("easy_3x3_cross", verbose=True)
    
    # Medium puzzle
    print("\n--- Medium Puzzle ---")
    collection.solve_puzzle("medium_4x4_multi", verbose=True)
    
    # Get random puzzle
    print("\n--- Random Puzzle ---")
    name, puzzle = collection.get_random_puzzle("easy")
    if puzzle:
        print(f"Random easy puzzle: {name}")
        collection.solve_puzzle(name, verbose=True)


if __name__ == "__main__":
    print("Flow Free Puzzle Collection")
    print("A curated collection of puzzles for testing and enjoyment\n")
    
    demo_puzzle_collection()
    
    print("\n" + "="*80)
    print("USAGE EXAMPLES:")
    print("="*80)
    print("""
# Basic usage
from puzzle_collection import PuzzleCollection
collection = PuzzleCollection()

# List all puzzles
collection.print_puzzle_catalog()

# Solve a specific puzzle
solution = collection.solve_puzzle("easy_3x3_cross")

# Get puzzles by difficulty
easy_puzzles = collection.get_puzzles_by_difficulty("easy")

# Solve all easy puzzles
collection.solve_all_puzzles("easy")

# Get a random puzzle
name, puzzle = collection.get_random_puzzle("medium")
""")
