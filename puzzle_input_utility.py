#!/usr/bin/env python3
"""
Flow Free Puzzle Input Utility

This utility provides multiple ways to input Flow Free puzzles and convert them
to the format expected by the FlowFreeSolver. It's designed to make it easy to
translate puzzles from pictures or other visual representations.

Usage:
    from puzzle_input_utility import PuzzleInputUtility
    
    # Method 1: Grid-based input (visual representation)
    utility = PuzzleInputUtility()
    puzzle = utility.create_from_grid([
        ['1', '.', '.', '2'],
        ['.', '.', '.', '.'],
        ['.', '.', '.', '.'],
        ['2', '.', '.', '1']
    ])
    
    # Method 2: Coordinate-based input
    puzzle = utility.create_from_coordinates(
        rows=4, cols=4,
        endpoints={
            1: [(0, 0), (3, 3)],
            2: [(0, 3), (3, 0)]
        }
    )
"""

import json
from typing import Dict, List, Tuple, Union, Optional
from main import FlowFreeSolver


class PuzzleInputUtility:
    """Utility class for creating Flow Free puzzles from various input formats."""
    
    def __init__(self):
        """Initialize the puzzle input utility."""
        self.puzzles = []  # Store created puzzles
        
    def create_from_grid(self, grid: List[List[str]], 
                        empty_char: str = '.') -> Dict:
        """
        Create a puzzle from a visual grid representation.
        
        Args:
            grid: 2D list of strings representing the puzzle grid
                  Use numbers/letters for endpoints, empty_char for empty cells
            empty_char: Character representing empty cells (default: '.')
            
        Returns:
            Dictionary with 'rows', 'cols', 'pairs' keys ready for FlowFreeSolver
            
        Example:
            grid = [
                ['1', '.', '.', '2'],
                ['.', '.', '.', '.'],
                ['.', '.', '.', '.'],
                ['2', '.', '.', '1']
            ]
            puzzle = utility.create_from_grid(grid)
        """
        if not grid or not grid[0]:
            raise ValueError("Grid cannot be empty")
            
        rows = len(grid)
        cols = len(grid[0])
        
        # Validate grid dimensions
        for i, row in enumerate(grid):
            if len(row) != cols:
                raise ValueError(f"Row {i} has {len(row)} columns, expected {cols}")
        
        # Find all symbols and their positions
        symbol_positions = {}
        
        for row_idx, row in enumerate(grid):
            for col_idx, cell in enumerate(row):
                if cell != empty_char and cell.strip():  # Non-empty, non-whitespace
                    symbol = cell.strip()
                    if symbol not in symbol_positions:
                        symbol_positions[symbol] = []
                    symbol_positions[symbol].append((row_idx, col_idx))
        
        # Validate that each symbol appears exactly twice
        pairs = {}
        for symbol, positions in symbol_positions.items():
            if len(positions) != 2:
                raise ValueError(f"Symbol '{symbol}' appears {len(positions)} times, must appear exactly 2 times")
            
            # Convert symbol to int if possible, otherwise keep as string
            try:
                symbol_key = int(symbol)
            except ValueError:
                symbol_key = symbol
                
            pairs[symbol_key] = (positions[0], positions[1])
        
        puzzle = {
            'rows': rows,
            'cols': cols,
            'pairs': pairs,
            'description': f"{rows}x{cols} puzzle with {len(pairs)} pairs"
        }
        
        self.puzzles.append(puzzle)
        return puzzle
    
    def create_from_coordinates(self, rows: int, cols: int, 
                              endpoints: Dict[Union[int, str], List[Tuple[int, int]]]) -> Dict:
        """
        Create a puzzle from coordinate specifications.
        
        Args:
            rows: Number of rows in the grid
            cols: Number of columns in the grid
            endpoints: Dictionary mapping symbols to list of two coordinate tuples
                      Example: {1: [(0, 0), (3, 3)], 2: [(0, 3), (3, 0)]}
            
        Returns:
            Dictionary with 'rows', 'cols', 'pairs' keys ready for FlowFreeSolver
        """
        if rows <= 0 or cols <= 0:
            raise ValueError("Rows and columns must be positive")
        
        pairs = {}
        for symbol, coords in endpoints.items():
            if len(coords) != 2:
                raise ValueError(f"Symbol '{symbol}' must have exactly 2 endpoints, got {len(coords)}")
            
            # Validate coordinates are within bounds
            for coord in coords:
                if not isinstance(coord, (tuple, list)) or len(coord) != 2:
                    raise ValueError(f"Coordinate {coord} must be a tuple/list of 2 integers")
                
                row, col = coord
                if not (0 <= row < rows and 0 <= col < cols):
                    raise ValueError(f"Coordinate {coord} is out of bounds for {rows}x{cols} grid")
            
            pairs[symbol] = (tuple(coords[0]), tuple(coords[1]))
        
        puzzle = {
            'rows': rows,
            'cols': cols,
            'pairs': pairs,
            'description': f"{rows}x{cols} puzzle with {len(pairs)} pairs"
        }
        
        self.puzzles.append(puzzle)
        return puzzle
    
    def create_from_string(self, puzzle_string: str, 
                          empty_char: str = '.') -> Dict:
        """
        Create a puzzle from a multi-line string representation.
        
        Args:
            puzzle_string: Multi-line string where each line is a row
            empty_char: Character representing empty cells
            
        Returns:
            Dictionary ready for FlowFreeSolver
            
        Example:
            puzzle_str = '''
            1 . . 2
            . . . .
            . . . .
            2 . . 1
            '''
            puzzle = utility.create_from_string(puzzle_str)
        """
        lines = [line.strip() for line in puzzle_string.strip().split('\n') if line.strip()]
        
        if not lines:
            raise ValueError("Puzzle string cannot be empty")
        
        # Split each line into cells (handle both space-separated and character-by-character)
        grid = []
        for line in lines:
            if ' ' in line:
                # Space-separated format
                row = [cell.strip() for cell in line.split()]
            else:
                # Character-by-character format
                row = list(line)
            grid.append(row)
        
        return self.create_from_grid(grid, empty_char)
    
    def validate_puzzle(self, puzzle: Dict) -> Tuple[bool, List[str]]:
        """
        Validate that a puzzle is properly formatted and solvable.
        
        Args:
            puzzle: Puzzle dictionary with 'rows', 'cols', 'pairs' keys
            
        Returns:
            Tuple of (is_valid, list_of_error_messages)
        """
        errors = []
        
        # Check required keys
        required_keys = ['rows', 'cols', 'pairs']
        for key in required_keys:
            if key not in puzzle:
                errors.append(f"Missing required key: {key}")
        
        if errors:
            return False, errors
        
        rows, cols, pairs = puzzle['rows'], puzzle['cols'], puzzle['pairs']
        
        # Validate dimensions
        if not isinstance(rows, int) or rows <= 0:
            errors.append(f"Rows must be a positive integer, got: {rows}")
        if not isinstance(cols, int) or cols <= 0:
            errors.append(f"Columns must be a positive integer, got: {cols}")
        
        # Validate pairs
        if not isinstance(pairs, dict):
            errors.append(f"Pairs must be a dictionary, got: {type(pairs)}")
            return False, errors
        
        if len(pairs) == 0:
            errors.append("Puzzle must have at least one pair")
        
        # Check each pair
        used_positions = set()
        for symbol, endpoints in pairs.items():
            if not isinstance(endpoints, (tuple, list)) or len(endpoints) != 2:
                errors.append(f"Symbol '{symbol}' endpoints must be a tuple/list of 2 coordinates")
                continue
            
            for i, coord in enumerate(endpoints):
                if not isinstance(coord, (tuple, list)) or len(coord) != 2:
                    errors.append(f"Symbol '{symbol}' endpoint {i+1} must be a coordinate tuple")
                    continue
                
                row, col = coord
                if not isinstance(row, int) or not isinstance(col, int):
                    errors.append(f"Symbol '{symbol}' coordinate ({row}, {col}) must be integers")
                    continue
                
                if not (0 <= row < rows and 0 <= col < cols):
                    errors.append(f"Symbol '{symbol}' coordinate ({row}, {col}) is out of bounds")
                    continue
                
                coord_tuple = (row, col)
                if coord_tuple in used_positions:
                    errors.append(f"Coordinate {coord_tuple} is used by multiple symbols")
                else:
                    used_positions.add(coord_tuple)
        
        # Check if puzzle is theoretically solvable (basic checks)
        total_cells = rows * cols
        endpoint_cells = len(used_positions)
        path_cells_needed = sum(self._estimate_min_path_length(endpoints) - 2 
                               for endpoints in pairs.values())
        
        if endpoint_cells + path_cells_needed > total_cells:
            errors.append(f"Puzzle may be unsolvable: need {endpoint_cells + path_cells_needed} cells but only have {total_cells}")
        
        return len(errors) == 0, errors
    
    def _estimate_min_path_length(self, endpoints: Tuple[Tuple[int, int], Tuple[int, int]]) -> int:
        """Estimate minimum path length between two endpoints (Manhattan distance)."""
        (r1, c1), (r2, c2) = endpoints
        return abs(r1 - r2) + abs(c1 - c2) + 1  # +1 because path includes both endpoints
    
    def print_puzzle_preview(self, puzzle: Dict, title: str = "Puzzle Preview"):
        """
        Print a visual preview of the puzzle showing only the endpoints.
        
        Args:
            puzzle: Puzzle dictionary
            title: Title for the preview
        """
        print(f"\n┌─ {title} ─" + "─" * max(0, 40 - len(title)) + "┐")
        
        rows, cols, pairs = puzzle['rows'], puzzle['cols'], puzzle['pairs']
        
        # Create grid with endpoints
        grid = [['·' for _ in range(cols)] for _ in range(rows)]
        
        for symbol, (start, end) in pairs.items():
            grid[start[0]][start[1]] = str(symbol)
            grid[end[0]][end[1]] = str(symbol)
        
        # Print grid
        for row in grid:
            line = "│ " + " ".join(f"{cell:^3}" for cell in row) + " │"
            print(line)
        
        print("└" + "─" * (len(line) - 1) + "┘")
        
        # Print pairs info
        print(f"\nPairs to connect:")
        for symbol, (start, end) in pairs.items():
            print(f"  {symbol}: {start} → {end}")
        print()
    
    def solve_puzzle(self, puzzle: Dict, verbose: bool = True) -> Optional[List[List[int]]]:
        """
        Solve a puzzle using the FlowFreeSolver.
        
        Args:
            puzzle: Puzzle dictionary
            verbose: Whether to print solving progress
            
        Returns:
            Solution grid as 2D list, or None if no solution found
        """
        # Validate puzzle first
        is_valid, errors = self.validate_puzzle(puzzle)
        if not is_valid:
            if verbose:
                print("❌ Puzzle validation failed:")
                for error in errors:
                    print(f"  - {error}")
            return None
        
        if verbose:
            self.print_puzzle_preview(puzzle, "Solving Puzzle")
        
        # Create solver and solve
        solver = FlowFreeSolver(puzzle['rows'], puzzle['cols'], puzzle['pairs'])
        
        if verbose:
            print("🚀 Starting solver...")
        
        if solver.solve():
            if verbose:
                solver.print_solution()
            return solver.get_solution_grid()
        else:
            if verbose:
                print("❌ No solution found")
            return None
    
    def get_puzzle_collection(self) -> List[Dict]:
        """Return all puzzles created with this utility."""
        return self.puzzles.copy()
    
    def clear_collection(self):
        """Clear the puzzle collection."""
        self.puzzles.clear()
    
    def save_puzzles_to_file(self, filename: str):
        """Save puzzle collection to a JSON file."""
        with open(filename, 'w') as f:
            json.dump(self.puzzles, f, indent=2)
        print(f"Saved {len(self.puzzles)} puzzles to {filename}")
    
    def load_puzzles_from_file(self, filename: str):
        """Load puzzles from a JSON file."""
        with open(filename, 'r') as f:
            loaded_puzzles = json.load(f)
        self.puzzles.extend(loaded_puzzles)
        print(f"Loaded {len(loaded_puzzles)} puzzles from {filename}")


def create_puzzle_from_picture_guide():
    """
    Print a guide for translating picture puzzles to text format.
    """
    print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    PICTURE TO PUZZLE TRANSLATION GUIDE                      ║
╚══════════════════════════════════════════════════════════════════════════════╝

When you have a Flow Free puzzle in picture format, follow these steps:

1. IDENTIFY THE GRID SIZE
   - Count rows and columns in the picture
   - Note: Most Flow Free puzzles are square (5x5, 6x6, etc.)

2. LOCATE THE ENDPOINTS
   - Find pairs of colored dots/circles/numbers
   - Each color/number should appear exactly twice
   - Note their positions (row, column) starting from (0,0) at top-left

3. CHOOSE YOUR INPUT METHOD:

   METHOD A - Grid Format (Recommended for visual puzzles):
   ```python
   utility = PuzzleInputUtility()
   puzzle = utility.create_from_grid([
       ['1', '.', '.', '2', '.'],
       ['.', '.', '.', '.', '.'],
       ['3', '.', '.', '.', '4'],
       ['.', '.', '.', '.', '.'],
       ['2', '.', '.', '1', '.']
   ])
   ```

   METHOD B - Coordinate Format (Good for precise input):
   ```python
   puzzle = utility.create_from_coordinates(
       rows=5, cols=5,
       endpoints={
           1: [(0, 0), (4, 3)],  # Red dots
           2: [(0, 3), (4, 0)],  # Blue dots
           3: [(2, 0), (2, 4)],  # Green dots
           4: [(2, 4), (2, 4)]   # Yellow dots
       }
   )
   ```

   METHOD C - String Format (Copy-paste friendly):
   ```python
   puzzle_str = '''
   1 . . 2 .
   . . . . .
   3 . . . 4
   . . . . .
   2 . . 1 .
   '''
   puzzle = utility.create_from_string(puzzle_str)
   ```

4. TIPS FOR ACCURACY:
   - Double-check coordinates (remember: row first, then column)
   - Use consistent symbols (numbers work well: 1, 2, 3, etc.)
   - Verify each symbol appears exactly twice
   - Test with the preview function before solving

5. COMMON MISTAKES TO AVOID:
   - Mixing up row/column order
   - Using same position for multiple endpoints
   - Forgetting that coordinates start at (0,0)
   - Using inconsistent symbols

Example workflow:
```python
from puzzle_input_utility import PuzzleInputUtility

utility = PuzzleInputUtility()

# Create your puzzle
puzzle = utility.create_from_grid([...])

# Preview it
utility.print_puzzle_preview(puzzle)

# Solve it
solution = utility.solve_puzzle(puzzle)
```
""")


if __name__ == "__main__":
    print("Flow Free Puzzle Input Utility")
    print("Use this utility to easily create puzzles from pictures or other formats.")
    print("\nFor help translating picture puzzles, run:")
    print("  from puzzle_input_utility import create_puzzle_from_picture_guide")
    print("  create_puzzle_from_picture_guide()")
