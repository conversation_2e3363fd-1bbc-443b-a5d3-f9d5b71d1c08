#!/usr/bin/env python3

"""
Test the user's specific puzzle from my_puzzles.json to ensure it can be solved
now that the pocket detection bug is fixed.
"""

from main import FlowFreeSolver

def test_user_puzzle():
    """Test the exact puzzle from the user's my_puzzles.json file."""
    print("=== Testing User's 5x5 Puzzle ===")
    
    # Exact puzzle from my_puzzles.json
    pairs = {
        "1": [(0, 0), (3, 4)],
        "2": [(2, 2), (3, 1)], 
        "3": [(3, 0), (4, 4)],
        "4": [(3, 2), (4, 0)]
    }
    
    solver = FlowFreeSolver(5, 5, pairs)
    solver.print_current_grid("User's 5x5 puzzle with 4 pairs")
    
    print("\n🚀 Attempting to solve the puzzle...")
    
    if solver.solve():
        print("\n🎉 SUCCESS: Puzzle solved!")
        solver.print_solution()
        return True
    else:
        print("\n❌ FAILED: Could not solve the puzzle")
        solver.print_current_grid("Final state (unsolved)")
        return False

def main():
    """Run the test."""
    success = test_user_puzzle()
    
    if success:
        print("\n✅ The pocket detection bug fix allows the user's puzzle to be solved!")
    else:
        print("\n❌ The puzzle still cannot be solved - there may be other issues.")

if __name__ == "__main__":
    main()
