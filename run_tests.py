#!/usr/bin/env python3
"""
Test runner for Flow Free solver.
Runs all available test cases and provides a summary.
"""

import sys
import time
from test_simple import test_simple_1x3
from test_5x5 import test_5x5_puzzle
from test_custom import test_custom_puzzle, test_another_custom

def run_all_tests():
    """Run all available test cases."""
    print("🧪 Flow Free Solver Test Suite")
    print("=" * 50)
    
    tests = [
        ("Simple 1x3 Puzzle", test_simple_1x3),
        ("Complex 5x5 Puzzle", test_5x5_puzzle),
        ("Custom Puzzle 1", test_custom_puzzle),
        ("Custom Puzzle 2", test_another_custom),
    ]
    
    results = []
    total_time = 0
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        print("-" * 30)
        
        start_time = time.time()
        try:
            success = test_func()
            end_time = time.time()
            duration = end_time - start_time
            total_time += duration
            
            results.append((test_name, success, duration))
            status = "✅ PASSED" if success else "❌ FAILED"
            print(f"Result: {status} ({duration:.2f}s)")
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            total_time += duration
            
            results.append((test_name, False, duration))
            print(f"Result: ❌ ERROR - {str(e)} ({duration:.2f}s)")
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    failed = 0
    
    for test_name, success, duration in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:<25} {status:>15} ({duration:>6.2f}s)")
        if success:
            passed += 1
        else:
            failed += 1
    
    print("-" * 50)
    print(f"Total Tests: {len(results)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Total Time: {total_time:.2f}s")
    
    if failed == 0:
        print("\n🎉 All tests passed!")
        return True
    else:
        print(f"\n⚠️  {failed} test(s) failed.")
        return False

def run_specific_test(test_name):
    """Run a specific test by name."""
    test_map = {
        "simple": ("Simple 1x3 Puzzle", test_simple_1x3),
        "5x5": ("Complex 5x5 Puzzle", test_5x5_puzzle),
        "custom1": ("Custom Puzzle 1", test_custom_puzzle),
        "custom2": ("Custom Puzzle 2", test_another_custom),
    }
    
    if test_name.lower() not in test_map:
        print(f"❌ Unknown test: {test_name}")
        print("Available tests:", ", ".join(test_map.keys()))
        return False
    
    name, test_func = test_map[test_name.lower()]
    print(f"🔍 Running: {name}")
    print("-" * 30)
    
    start_time = time.time()
    success = test_func()
    duration = time.time() - start_time
    
    status = "✅ PASSED" if success else "❌ FAILED"
    print(f"\nResult: {status} ({duration:.2f}s)")
    
    return success

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Run specific test
        test_name = sys.argv[1]
        run_specific_test(test_name)
    else:
        # Run all tests
        run_all_tests()
