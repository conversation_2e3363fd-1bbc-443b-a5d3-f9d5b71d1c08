#!/usr/bin/env python3

"""
Test to verify the fix for the pocket detection bug.

The bug was: when symbol 1 moves from (0,0) to (0,1) to (0,2) to (0,3) to (0,4) to (1,4),
the solver incorrectly thinks that cell (2,4) is surrounded by the path and forms a pocket/bay.
This is not true - the cell (2,4) is not pocketed.
"""

from main import FlowFreeSolver

def test_pocket_bug_fix():
    """Test the specific case that was incorrectly flagged as creating a pocket."""
    print("=== Testing Pocket Detection Bug Fix ===")
    
    # Create the exact puzzle from the user's example
    pairs = {
        "1": [(0, 0), (3, 4)],
        "2": [(2, 2), (3, 1)], 
        "3": [(3, 0), (4, 4)],
        "4": [(3, 2), (4, 0)]
    }
    
    solver = FlowFreeSolver(5, 5, pairs)
    solver.print_current_grid("Initial 5x5 puzzle")
    
    print("\n🔍 Testing the specific path that was incorrectly flagged:")
    print("Symbol 1: (0,0) → (0,1) → (0,2) → (0,3) → (0,4) → (1,4)")
    
    # Manually trace the path to see if it gets flagged incorrectly
    test_path = [(0, 0), (0, 1), (0, 2), (0, 3), (0, 4), (1, 4)]
    
    # Set up the grid state as if we're building this path
    for i, (row, col) in enumerate(test_path[:-1]):  # Don't place the last position yet
        if i > 0:  # Don't overwrite the starting endpoint
            solver.grid[row][col] = "1"
    
    print("\nGrid state before the problematic move to (1,4):")
    solver.print_current_grid("Before move to (1,4)")
    
    # Now test if moving to (1,4) incorrectly triggers pocket detection
    print(f"\n🧪 Testing bad shape detection for path: {test_path}")
    
    # Temporarily place the symbol at (1,4) to test
    solver.grid[1][4] = "1"
    
    print("Grid state after move to (1,4):")
    solver.print_current_grid("After move to (1,4)")
    
    # Test the bad shape detection
    has_bad_shape = solver.has_bad_shape(test_path, "1")
    
    print(f"\n📊 Result: has_bad_shape = {has_bad_shape}")
    
    if has_bad_shape:
        print("❌ FAILED: The path is still incorrectly flagged as having a bad shape")
        print("   This means the pocket detection bug is not fixed")
        return False
    else:
        print("✅ SUCCESS: The path is correctly NOT flagged as having a bad shape")
        print("   The pocket detection bug appears to be fixed")
        return True

def test_actual_pocket_detection():
    """Test that we still correctly detect actual pockets."""
    print("\n=== Testing Actual Pocket Detection ===")
    
    # Create a puzzle that should actually create a pocket
    pairs = {
        "1": [(0, 0), (2, 2)]
    }
    
    solver = FlowFreeSolver(3, 3, pairs)
    
    # Create a path that actually forms a pocket
    # This should surround the cell (1,1) in a way that makes it unreachable
    actual_pocket_path = [(0, 0), (0, 1), (0, 2), (1, 2), (2, 2)]
    
    # Set up the grid
    for i, (row, col) in enumerate(actual_pocket_path[:-1]):
        if i > 0:  # Don't overwrite the starting endpoint  
            solver.grid[row][col] = "1"
    
    print("Grid state that should create an actual pocket:")
    solver.print_current_grid("Actual pocket test")
    
    # Test if this correctly detects a bad shape
    solver.grid[1][2] = "1"  # Place the last move
    has_bad_shape = solver.has_bad_shape(actual_pocket_path, "1")
    
    print(f"\n📊 Result for actual pocket: has_bad_shape = {has_bad_shape}")
    
    if has_bad_shape:
        print("✅ SUCCESS: Actual pocket is correctly detected")
        return True
    else:
        print("⚠️  WARNING: Actual pocket was not detected - may need to adjust sensitivity")
        return False

def main():
    """Run all tests."""
    print("🧪 Running pocket detection bug fix tests...\n")
    
    test1_passed = test_pocket_bug_fix()
    test2_passed = test_actual_pocket_detection()
    
    print(f"\n📊 Test Results:")
    print(f"   Bug fix test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"   Actual pocket test: {'✅ PASSED' if test2_passed else '⚠️  WARNING'}")
    
    if test1_passed:
        print("\n🎉 The pocket detection bug appears to be fixed!")
    else:
        print("\n❌ The pocket detection bug still exists and needs more work.")

if __name__ == "__main__":
    main()
