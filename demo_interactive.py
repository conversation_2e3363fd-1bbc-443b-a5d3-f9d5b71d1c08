#!/usr/bin/env python3
"""
Demo script showing how to use the interactive puzzle input tool.
"""

from interactive_puzzle_input import InteractivePuzzleInput, solve_puzzles_from_file, quick_add_puzzle
import json
import os


def create_demo_puzzles():
    """Create a demo puzzle file to show the functionality."""
    demo_puzzles = [
        {
            "puzzle": {
                "rows": 1,
                "cols": 3,
                "pairs": {
                    "1": [[0, 0], [0, 2]]
                },
                "description": "Simple 1x3 line"
            },
            "description": "Simple 1x3 line",
            "created": "2024-01-01 12:00:00"
        },
        {
            "puzzle": {
                "rows": 3,
                "cols": 3,
                "pairs": {
                    "A": [[0, 1], [2, 1]],
                    "B": [[1, 0], [1, 2]]
                },
                "description": "3x3 cross pattern"
            },
            "description": "3x3 cross pattern",
            "created": "2024-01-01 12:01:00"
        }
    ]
    
    with open("demo_puzzles.json", "w") as f:
        json.dump(demo_puzzles, f, indent=2)
    
    print("✅ Created demo_puzzles.json with sample puzzles")


def demo_batch_solving():
    """Demonstrate batch solving functionality."""
    print("🚀 DEMO: Batch Solving")
    print("=" * 40)
    
    # Create demo file if it doesn't exist
    if not os.path.exists("demo_puzzles.json"):
        create_demo_puzzles()
    
    # Solve all puzzles in the file
    solve_puzzles_from_file("demo_puzzles.json")


def demo_interactive_usage():
    """Show how to use the interactive tool programmatically."""
    print("\n🎮 DEMO: Interactive Tool Usage")
    print("=" * 40)
    
    tool = InteractivePuzzleInput()
    
    # Simulate adding a puzzle programmatically
    from puzzle_input_utility import PuzzleInputUtility
    utility = PuzzleInputUtility()
    
    # Create a simple puzzle
    puzzle = utility.create_from_grid([
        ['1', '.', '2'],
        ['.', '.', '.'],
        ['2', '.', '1']
    ])
    
    # Add it to the tool's collection
    puzzle_entry = {
        'puzzle': puzzle,
        'description': 'Demo 3x3 diagonal puzzle',
        'created': tool.get_timestamp()
    }
    
    tool.puzzles.append(puzzle_entry)
    
    print("✅ Added demo puzzle to collection")
    
    # Show the collection
    tool.view_current_collection()
    
    # Solve the puzzles
    tool.solve_all_puzzles()


def show_usage_examples():
    """Show different ways to use the interactive tool."""
    print("\n📚 USAGE EXAMPLES")
    print("=" * 40)
    
    print("""
🎯 COMMAND LINE USAGE:

1. Interactive Mode (full menu):
   python interactive_puzzle_input.py

2. Quick add a single puzzle:
   python interactive_puzzle_input.py add

3. Solve all puzzles in default file:
   python interactive_puzzle_input.py solve

4. Solve puzzles in specific file:
   python interactive_puzzle_input.py solve my_puzzles.json

5. Show help:
   python interactive_puzzle_input.py help

🎯 INTERACTIVE MODE FEATURES:

📝 Add New Puzzle:
   - Enter grid size (e.g., "5" for 5x5, "3x4" for 3×4)
   - Input each row (use numbers/letters for endpoints, '.' for empty)
   - Automatic validation
   - Optional description
   - Option to solve immediately

📁 Load Puzzles:
   - Load from JSON file
   - Supports multiple formats
   - Automatic coordinate conversion
   - Shows summary of loaded puzzles

🚀 Solve All Puzzles:
   - Solves all puzzles in current collection
   - Shows progress and results
   - Detailed statistics

💾 Save Collection:
   - Save to JSON file
   - Preserves metadata (description, creation time)
   - Overwrite protection

📊 View Collection:
   - Shows all puzzles with details
   - Size, pairs, symbols, creation time

🔄 Clear Collection:
   - Remove all puzzles from memory
   - Confirmation required

🎯 PUZZLE INPUT TIPS:

For a 4x4 puzzle from a picture:
1. Count: 4 rows, 4 columns
2. Identify endpoints: Red at (0,0) and (3,3), Blue at (0,3) and (3,0)
3. Enter grid size: 4
4. Enter rows:
   Row 1: R . . B
   Row 2: . . . .
   Row 3: . . . .
   Row 4: B . . R

The tool will:
- Validate the puzzle
- Convert to solver format
- Optionally solve immediately
- Save to collection
""")


def main():
    """Run the demonstration."""
    print("🧩 INTERACTIVE PUZZLE INPUT TOOL DEMO")
    print("=" * 50)
    
    # Demo batch solving
    demo_batch_solving()
    
    # Demo interactive usage
    demo_interactive_usage()
    
    # Show usage examples
    show_usage_examples()
    
    print("\n🎯 NEXT STEPS:")
    print("=" * 20)
    print("1. Run: python interactive_puzzle_input.py")
    print("2. Choose option 1 to add your first puzzle")
    print("3. Enter the grid size and puzzle data")
    print("4. Save your collection and solve!")
    print("\nHappy puzzle solving! 🧩")


if __name__ == "__main__":
    main()
