#!/usr/bin/env python3
"""
Complex 5x5 Flow Free puzzle test case.
This tests the solver's ability to handle multiple paths and avoid bad shapes.
"""

from main import FlowFreeSolver

def test_5x5_puzzle():
    """Test a complex 5x5 puzzle with 5 different symbols."""
    print("=== Complex 5x5 Puzzle Test ===")
    
    # Define the puzzle: 5 pairs to connect in a 5x5 grid
    pairs = {
        1: ((0, 0), (4, 1)),  # Connect top-left to bottom-center-left
        2: ((0, 2), (3, 1)),  # Connect top-center to bottom-left-center
        3: ((1, 2), (4, 2)),  # Connect center-left to bottom-center
        4: ((0, 4), (3, 3)),  # Connect top-right to bottom-right-center
        5: ((1, 4), (4, 3))   # Connect center-right to bottom-right
    }
    
    # Create and display the puzzle
    solver = FlowFreeSolver(5, 5, pairs)
    solver.print_current_grid("Complex 5x5 Puzzle")
    
    # Solve the puzzle
    print("🚀 Starting solver...")
    if solver.solve():
        solver.print_solution()
        return True
    else:
        print("❌ No solution found")
        solver.print_current_grid("Final State (No Solution)")
        return False

if __name__ == "__main__":
    success = test_5x5_puzzle()
    if success:
        print("\n✅ Test passed!")
    else:
        print("\n❌ Test failed!")
