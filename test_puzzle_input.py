#!/usr/bin/env python3
"""
Test file demonstrating the Puzzle Input Utility.

This file shows various ways to create Flow Free puzzles using the utility,
including examples that simulate translating puzzles from pictures.
"""

from puzzle_input_utility import PuzzleInputUtility, create_puzzle_from_picture_guide


def test_grid_input_method():
    """Test creating puzzles using the grid input method."""
    print("="*60)
    print("TEST 1: Grid Input Method")
    print("="*60)
    
    utility = PuzzleInputUtility()
    
    # Example 1: Simple 3x3 puzzle
    print("\n--- Example 1: Simple 3x3 Cross Pattern ---")
    puzzle1 = utility.create_from_grid([
        ['1', '.', '2'],
        ['.', '.', '.'],
        ['2', '.', '1']
    ])
    
    utility.print_puzzle_preview(puzzle1, "3x3 Cross Pattern")
    solution1 = utility.solve_puzzle(puzzle1, verbose=False)
    print(f"Solution found: {'✅ Yes' if solution1 else '❌ No'}")
    
    # Example 2: 4x4 puzzle with multiple pairs
    print("\n--- Example 2: 4x4 Multi-pair Puzzle ---")
    puzzle2 = utility.create_from_grid([
        ['1', '.', '.', '2'],
        ['.', '3', '4', '.'],
        ['.', '4', '3', '.'],
        ['2', '.', '.', '1']
    ])
    
    utility.print_puzzle_preview(puzzle2, "4x4 Multi-pair Puzzle")
    solution2 = utility.solve_puzzle(puzzle2, verbose=False)
    print(f"Solution found: {'✅ Yes' if solution2 else '❌ No'}")
    
    return solution1 is not None and solution2 is not None


def test_coordinate_input_method():
    """Test creating puzzles using coordinate input method."""
    print("\n" + "="*60)
    print("TEST 2: Coordinate Input Method")
    print("="*60)
    
    utility = PuzzleInputUtility()
    
    # Example 1: 5x5 puzzle (similar to test_5x5.py but using utility)
    print("\n--- Example 1: 5x5 Complex Puzzle ---")
    puzzle1 = utility.create_from_coordinates(
        rows=5, cols=5,
        endpoints={
            1: [(0, 0), (4, 1)],  # Connect top-left to bottom-center-left
            2: [(0, 2), (3, 1)],  # Connect top-center to bottom-left-center
            3: [(1, 2), (4, 2)],  # Connect center-left to bottom-center
            4: [(0, 4), (3, 3)],  # Connect top-right to bottom-right-center
            5: [(1, 4), (4, 3)]   # Connect center-right to bottom-right
        }
    )
    
    utility.print_puzzle_preview(puzzle1, "5x5 Complex Puzzle")
    solution1 = utility.solve_puzzle(puzzle1, verbose=False)
    print(f"Solution found: {'✅ Yes' if solution1 else '❌ No'}")
    
    # Example 2: 6x6 puzzle with letters instead of numbers
    print("\n--- Example 2: 6x6 Puzzle with Letters ---")
    puzzle2 = utility.create_from_coordinates(
        rows=6, cols=6,
        endpoints={
            'A': [(0, 0), (5, 5)],  # Diagonal connection
            'B': [(0, 5), (5, 0)],  # Other diagonal
            'C': [(2, 1), (3, 4)],  # Middle connection
            'D': [(1, 2), (4, 3)]   # Another middle connection
        }
    )
    
    utility.print_puzzle_preview(puzzle2, "6x6 Letter Puzzle")
    solution2 = utility.solve_puzzle(puzzle2, verbose=False)
    print(f"Solution found: {'✅ Yes' if solution2 else '❌ No'}")
    
    return solution1 is not None


def test_string_input_method():
    """Test creating puzzles using string input method."""
    print("\n" + "="*60)
    print("TEST 3: String Input Method")
    print("="*60)
    
    utility = PuzzleInputUtility()
    
    # Example 1: Space-separated format
    print("\n--- Example 1: Space-separated Format ---")
    puzzle_str1 = """
    1 . . 2
    . . . .
    . . . .
    2 . . 1
    """
    
    puzzle1 = utility.create_from_string(puzzle_str1)
    utility.print_puzzle_preview(puzzle1, "String Input Puzzle 1")
    solution1 = utility.solve_puzzle(puzzle1, verbose=False)
    print(f"Solution found: {'✅ Yes' if solution1 else '❌ No'}")
    
    # Example 2: Character-by-character format
    print("\n--- Example 2: Character Format ---")
    puzzle_str2 = """
    1..2
    ....
    ....
    2..1
    """
    
    puzzle2 = utility.create_from_string(puzzle_str2)
    utility.print_puzzle_preview(puzzle2, "String Input Puzzle 2")
    solution2 = utility.solve_puzzle(puzzle2, verbose=False)
    print(f"Solution found: {'✅ Yes' if solution2 else '❌ No'}")
    
    return solution1 is not None and solution2 is not None


def test_picture_translation_examples():
    """Test examples that simulate translating from picture puzzles."""
    print("\n" + "="*60)
    print("TEST 4: Picture Translation Examples")
    print("="*60)
    
    utility = PuzzleInputUtility()
    
    # Example 1: Simulating a typical mobile game puzzle
    print("\n--- Example 1: Mobile Game Style (5x5) ---")
    print("Imagine you see this in a picture:")
    print("  Red dots at corners, Blue dots on edges, Green in middle")
    
    puzzle1 = utility.create_from_grid([
        ['R', '.', 'B', '.', 'R'],
        ['.', '.', '.', '.', '.'],
        ['B', '.', 'G', '.', 'Y'],
        ['.', '.', '.', '.', '.'],
        ['Y', '.', 'G', '.', 'P']
    ])
    
    utility.print_puzzle_preview(puzzle1, "Mobile Game Style Puzzle")
    solution1 = utility.solve_puzzle(puzzle1, verbose=False)
    print(f"Solution found: {'✅ Yes' if solution1 else '❌ No'}")
    
    # Example 2: Simulating a puzzle book format
    print("\n--- Example 2: Puzzle Book Style (4x4) ---")
    print("Imagine numbered circles in a puzzle book:")
    
    puzzle2 = utility.create_from_coordinates(
        rows=4, cols=4,
        endpoints={
            1: [(0, 1), (3, 2)],  # Number 1 circles
            2: [(0, 2), (2, 0)],  # Number 2 circles
            3: [(1, 3), (3, 1)],  # Number 3 circles
            4: [(2, 3), (3, 0)]   # Number 4 circles
        }
    )
    
    utility.print_puzzle_preview(puzzle2, "Puzzle Book Style")
    solution2 = utility.solve_puzzle(puzzle2, verbose=False)
    print(f"Solution found: {'✅ Yes' if solution2 else '❌ No'}")
    
    return solution1 is not None


def test_validation_and_error_handling():
    """Test puzzle validation and error handling."""
    print("\n" + "="*60)
    print("TEST 5: Validation and Error Handling")
    print("="*60)
    
    utility = PuzzleInputUtility()
    
    # Test 1: Invalid grid (symbol appears only once)
    print("\n--- Test 1: Invalid Puzzle (Symbol appears once) ---")
    try:
        puzzle1 = utility.create_from_grid([
            ['1', '.', '.'],
            ['.', '.', '.'],
            ['.', '.', '2']  # Missing second '1'
        ])
        print("❌ Should have failed but didn't")
        return False
    except ValueError as e:
        print(f"✅ Correctly caught error: {e}")
    
    # Test 2: Out of bounds coordinates
    print("\n--- Test 2: Out of Bounds Coordinates ---")
    try:
        puzzle2 = utility.create_from_coordinates(
            rows=3, cols=3,
            endpoints={
                1: [(0, 0), (5, 5)]  # (5,5) is out of bounds for 3x3
            }
        )
        print("❌ Should have failed but didn't")
        return False
    except ValueError as e:
        print(f"✅ Correctly caught error: {e}")
    
    # Test 3: Valid puzzle validation
    print("\n--- Test 3: Valid Puzzle Validation ---")
    puzzle3 = utility.create_from_grid([
        ['1', '.', '2'],
        ['.', '.', '.'],
        ['2', '.', '1']
    ])
    
    is_valid, errors = utility.validate_puzzle(puzzle3)
    print(f"Validation result: {'✅ Valid' if is_valid else '❌ Invalid'}")
    if errors:
        print("Errors:", errors)
    
    return True


def test_collection_management():
    """Test puzzle collection and file operations."""
    print("\n" + "="*60)
    print("TEST 6: Collection Management")
    print("="*60)
    
    utility = PuzzleInputUtility()
    
    # Create several puzzles
    puzzle1 = utility.create_from_grid([['1', '1']])  # Simple 1x2
    puzzle2 = utility.create_from_grid([
        ['1', '.', '2'],
        ['2', '.', '1']
    ])  # 2x3
    
    collection = utility.get_puzzle_collection()
    print(f"Created {len(collection)} puzzles in collection")
    
    # Test saving and loading (commented out to avoid file operations in test)
    # utility.save_puzzles_to_file("test_puzzles.json")
    # utility.clear_collection()
    # utility.load_puzzles_from_file("test_puzzles.json")
    
    print("✅ Collection management test completed")
    return True


def run_all_tests():
    """Run all tests and provide a summary."""
    print("🚀 STARTING PUZZLE INPUT UTILITY TESTS")
    print("="*80)
    
    tests = [
        ("Grid Input Method", test_grid_input_method),
        ("Coordinate Input Method", test_coordinate_input_method),
        ("String Input Method", test_string_input_method),
        ("Picture Translation Examples", test_picture_translation_examples),
        ("Validation and Error Handling", test_validation_and_error_handling),
        ("Collection Management", test_collection_management)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"\n{'✅ PASSED' if result else '❌ FAILED'}: {test_name}")
        except Exception as e:
            results.append((test_name, False))
            print(f"\n❌ ERROR in {test_name}: {e}")
    
    # Summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {status}: {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The utility is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total


def demo_picture_translation():
    """Demonstrate how to translate a picture puzzle step by step."""
    print("\n" + "="*80)
    print("DEMO: TRANSLATING A PICTURE PUZZLE")
    print("="*80)
    
    print("""
Let's say you have a picture of this 5x5 Flow Free puzzle:

Picture description:
- Red circles at (0,0) and (4,4)
- Blue circles at (0,4) and (4,0)  
- Green circles at (1,1) and (3,3)
- Yellow circles at (1,3) and (3,1)
- Purple circles at (2,0) and (2,4)

Step 1: Choose your input method
""")
    
    utility = PuzzleInputUtility()
    
    # Method 1: Grid representation
    print("Method 1 - Grid representation:")
    puzzle1 = utility.create_from_grid([
        ['R', '.', '.', '.', 'B'],
        ['.', 'G', '.', 'Y', '.'],
        ['P', '.', '.', '.', 'P'],
        ['.', 'Y', '.', 'G', '.'],
        ['B', '.', '.', '.', 'R']
    ])
    
    utility.print_puzzle_preview(puzzle1, "Picture Puzzle - Grid Method")
    
    # Method 2: Coordinate specification
    print("\nMethod 2 - Coordinate specification:")
    puzzle2 = utility.create_from_coordinates(
        rows=5, cols=5,
        endpoints={
            'R': [(0, 0), (4, 4)],  # Red
            'B': [(0, 4), (4, 0)],  # Blue
            'G': [(1, 1), (3, 3)],  # Green
            'Y': [(1, 3), (3, 1)],  # Yellow
            'P': [(2, 0), (2, 4)]   # Purple
        }
    )
    
    utility.print_puzzle_preview(puzzle2, "Picture Puzzle - Coordinate Method")
    
    print("Step 2: Solve the puzzle")
    solution = utility.solve_puzzle(puzzle1, verbose=True)
    
    if solution:
        print("🎉 Successfully translated and solved the picture puzzle!")
    else:
        print("❌ Puzzle couldn't be solved - check the translation")


if __name__ == "__main__":
    print("Flow Free Puzzle Input Utility - Test Suite")
    print("This demonstrates various ways to input puzzles from pictures\n")
    
    # Show the picture translation guide
    create_puzzle_from_picture_guide()
    
    # Run all tests
    success = run_all_tests()
    
    # Show demo
    demo_picture_translation()
    
    print("\n" + "="*80)
    print("NEXT STEPS:")
    print("="*80)
    print("1. Use puzzle_input_utility.py to create your own puzzles")
    print("2. Check out puzzle_collection.py for pre-made puzzles")
    print("3. Try translating puzzles from pictures using the guide above")
    print("4. Save your puzzle collections to JSON files for later use")
