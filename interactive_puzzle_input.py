#!/usr/bin/env python3
"""
Interactive Flow Free Puzzle Input Tool

This tool provides a command-line interface for entering Flow Free puzzles
and managing puzzle collections. Perfect for quickly inputting puzzles from
pictures or other sources.

Usage:
    python interactive_puzzle_input.py
"""

import json
import os
from typing import List, Dict, Optional
from puzzle_input_utility import PuzzleInputUtility
from main import FlowFreeSolver


class InteractivePuzzleInput:
    """Interactive command-line tool for puzzle input and management."""
    
    def __init__(self, default_filename: str = "my_puzzles.json"):
        """Initialize the interactive tool."""
        self.utility = PuzzleInputUtility()
        self.default_filename = default_filename
        self.puzzles = []
        
    def run(self):
        """Run the interactive puzzle input tool."""
        print("🧩 FLOW FREE INTERACTIVE PUZZLE INPUT TOOL")
        print("="*50)
        print("Enter puzzles from pictures, solve collections, and more!")
        print()
        
        while True:
            print("\n📋 MAIN MENU:")
            print("1. 📝 Add new puzzle")
            print("2. 📁 Load puzzles from file")
            print("3. 🚀 Solve all puzzles in current collection")
            print("4. 💾 Save current collection to file")
            print("5. 📊 View current collection")
            print("6. 🔄 Clear current collection")
            print("7. ❌ Exit")
            
            choice = input("\nSelect option (1-7): ").strip()
            
            if choice == '1':
                self.add_new_puzzle()
            elif choice == '2':
                self.load_puzzles_from_file()
            elif choice == '3':
                self.solve_all_puzzles()
            elif choice == '4':
                self.save_puzzles_to_file()
            elif choice == '5':
                self.view_current_collection()
            elif choice == '6':
                self.clear_collection()
            elif choice == '7':
                print("\n👋 Thanks for using the puzzle input tool!")
                break
            else:
                print("❌ Invalid choice. Please select 1-7.")
    
    def add_new_puzzle(self):
        """Interactive puzzle input process."""
        print("\n📝 ADD NEW PUZZLE")
        print("-" * 30)
        
        while True:
            try:
                # Get grid size
                print("\n🔢 Enter grid size:")
                size = input("Grid size (e.g., 5 for 5x5, or 3x4 for 3 rows × 4 cols): ").strip()
                
                if 'x' in size.lower():
                    # Handle rectangular grids like "3x4"
                    rows, cols = map(int, size.lower().split('x'))
                else:
                    # Handle square grids like "5"
                    rows = cols = int(size)
                
                if rows <= 0 or cols <= 0 or rows > 20 or cols > 20:
                    print("❌ Grid size must be between 1 and 20")
                    continue
                
                break
                
            except ValueError:
                print("❌ Invalid format. Use '5' for 5x5 or '3x4' for 3×4")
        
        print(f"\n📐 Creating {rows}×{cols} grid")
        print("💡 Tips:")
        print("  - Use numbers (1,2,3...) or letters (A,B,C...) for endpoints")
        print("  - Use '.' or space for empty cells")
        print("  - Each symbol must appear exactly twice")
        print("  - Press Enter after each row")
        print()
        
        # Input grid data
        grid = []
        for row in range(rows):
            while True:
                print(f"Row {row + 1}/{rows}: ", end="")
                row_input = input().strip()
                
                if not row_input:
                    print("❌ Row cannot be empty")
                    continue
                
                # Parse row input (handle both spaced and non-spaced)
                if ' ' in row_input:
                    row_data = row_input.split()
                else:
                    row_data = list(row_input)
                
                # Clean up the data
                row_data = [cell.strip() if cell.strip() else '.' for cell in row_data]
                
                if len(row_data) != cols:
                    print(f"❌ Row must have exactly {cols} cells, got {len(row_data)}")
                    continue
                
                grid.append(row_data)
                break
        
        # Display the entered grid
        print(f"\n📋 Your {rows}×{cols} puzzle:")
        self.display_grid(grid)
        
        # Validate the puzzle
        try:
            puzzle = self.utility.create_from_grid(grid)
            is_valid, errors = self.utility.validate_puzzle(puzzle)
            
            if is_valid:
                print("✅ Puzzle is valid!")
                
                # Add description
                description = input("\n📝 Enter description (optional): ").strip()
                if not description:
                    pairs_count = len(puzzle['pairs'])
                    description = f"{rows}×{cols} puzzle with {pairs_count} pairs"
                
                # Add to collection
                puzzle_entry = {
                    'puzzle': puzzle,
                    'description': description,
                    'created': self.get_timestamp()
                }
                
                self.puzzles.append(puzzle_entry)
                print(f"✅ Puzzle added to collection! (Total: {len(self.puzzles)})")
                
                # Ask if user wants to solve it now
                if input("\n🚀 Solve this puzzle now? (y/n): ").lower().startswith('y'):
                    self.solve_single_puzzle(puzzle, description)
                
            else:
                print("❌ Puzzle validation failed:")
                for error in errors:
                    print(f"  - {error}")
                print("Puzzle not added to collection.")
                
        except Exception as e:
            print(f"❌ Error creating puzzle: {e}")
            print("Puzzle not added to collection.")
        
        # Ask if user wants to add another
        if input("\n➕ Add another puzzle? (y/n): ").lower().startswith('y'):
            self.add_new_puzzle()
    
    def display_grid(self, grid: List[List[str]]):
        """Display a grid in a nice format."""
        print("   " + "─" * (len(grid[0]) * 4 + 1))
        for i, row in enumerate(grid):
            row_str = f"{i+1:2} │ " + " │ ".join(f"{cell:^1}" for cell in row) + " │"
            print(row_str)
        print("   " + "─" * (len(grid[0]) * 4 + 1))
    
    def load_puzzles_from_file(self):
        """Load puzzles from a JSON file."""
        print("\n📁 LOAD PUZZLES FROM FILE")
        print("-" * 30)
        
        filename = input(f"Enter filename (default: {self.default_filename}): ").strip()
        if not filename:
            filename = self.default_filename
        
        try:
            if not os.path.exists(filename):
                print(f"❌ File '{filename}' not found")
                return
            
            with open(filename, 'r') as f:
                loaded_data = json.load(f)
            
            # Handle different file formats
            if isinstance(loaded_data, list):
                # New format with metadata
                loaded_puzzles = loaded_data
            elif isinstance(loaded_data, dict) and 'puzzles' in loaded_data:
                # Alternative format
                loaded_puzzles = loaded_data['puzzles']
            else:
                # Legacy format - just puzzle data
                loaded_puzzles = [{'puzzle': loaded_data, 'description': 'Loaded puzzle', 'created': 'Unknown'}]
            
            # Add to current collection
            old_count = len(self.puzzles)
            self.puzzles.extend(loaded_puzzles)
            new_count = len(loaded_puzzles)
            
            print(f"✅ Loaded {new_count} puzzles from '{filename}'")
            print(f"📊 Total puzzles in collection: {len(self.puzzles)}")
            
            # Show summary
            if new_count > 0:
                print("\n📋 Loaded puzzles:")
                for i, entry in enumerate(loaded_puzzles):
                    puzzle = entry['puzzle']
                    desc = entry.get('description', 'No description')
                    size = f"{puzzle['rows']}×{puzzle['cols']}"
                    pairs = len(puzzle['pairs'])
                    print(f"  {old_count + i + 1}. {size} - {pairs} pairs - {desc}")
            
        except json.JSONDecodeError:
            print(f"❌ Invalid JSON format in '{filename}'")
        except Exception as e:
            print(f"❌ Error loading file: {e}")
    
    def solve_all_puzzles(self):
        """Solve all puzzles in the current collection."""
        if not self.puzzles:
            print("\n❌ No puzzles in collection. Add some puzzles first!")
            return
        
        print(f"\n🚀 SOLVING ALL PUZZLES ({len(self.puzzles)} total)")
        print("=" * 50)
        
        results = []
        for i, entry in enumerate(self.puzzles):
            puzzle = entry['puzzle']
            description = entry.get('description', f'Puzzle {i+1}')
            
            print(f"\n🎯 Puzzle {i+1}/{len(self.puzzles)}: {description}")
            print(f"   Size: {puzzle['rows']}×{puzzle['cols']}, Pairs: {len(puzzle['pairs'])}")
            
            # Show puzzle preview
            self.utility.print_puzzle_preview(puzzle, f"Puzzle {i+1}")
            
            # Solve
            solution = self.utility.solve_puzzle(puzzle, verbose=False)
            success = solution is not None
            results.append(success)
            
            if success:
                print("✅ SOLVED!")
            else:
                print("❌ Could not solve")
        
        # Summary
        solved = sum(results)
        total = len(results)
        print(f"\n📊 SOLVING SUMMARY")
        print("=" * 30)
        print(f"Solved: {solved}/{total} puzzles")
        print(f"Success rate: {solved/total*100:.1f}%")
        
        if solved == total:
            print("🎉 All puzzles solved successfully!")
        elif solved > 0:
            print("⚠️  Some puzzles couldn't be solved (may be too complex)")
        else:
            print("❌ No puzzles could be solved")
    
    def solve_single_puzzle(self, puzzle: Dict, description: str = ""):
        """Solve a single puzzle with full output."""
        print(f"\n🚀 SOLVING: {description}")
        print("-" * 40)
        
        solution = self.utility.solve_puzzle(puzzle, verbose=True)
        
        if solution:
            print("🎉 Puzzle solved successfully!")
        else:
            print("❌ Could not solve this puzzle")
    
    def save_puzzles_to_file(self):
        """Save current collection to a JSON file."""
        if not self.puzzles:
            print("\n❌ No puzzles to save. Add some puzzles first!")
            return
        
        print(f"\n💾 SAVE PUZZLES TO FILE")
        print("-" * 30)
        
        filename = input(f"Enter filename (default: {self.default_filename}): ").strip()
        if not filename:
            filename = self.default_filename
        
        try:
            # Check if file exists
            if os.path.exists(filename):
                overwrite = input(f"⚠️  File '{filename}' exists. Overwrite? (y/n): ")
                if not overwrite.lower().startswith('y'):
                    print("❌ Save cancelled")
                    return
            
            with open(filename, 'w') as f:
                json.dump(self.puzzles, f, indent=2)
            
            print(f"✅ Saved {len(self.puzzles)} puzzles to '{filename}'")
            
        except Exception as e:
            print(f"❌ Error saving file: {e}")
    
    def view_current_collection(self):
        """Display current puzzle collection."""
        print(f"\n📊 CURRENT COLLECTION ({len(self.puzzles)} puzzles)")
        print("=" * 50)
        
        if not self.puzzles:
            print("No puzzles in collection.")
            return
        
        for i, entry in enumerate(self.puzzles):
            puzzle = entry['puzzle']
            description = entry.get('description', 'No description')
            created = entry.get('created', 'Unknown')
            size = f"{puzzle['rows']}×{puzzle['cols']}"
            pairs = len(puzzle['pairs'])
            
            print(f"\n🧩 Puzzle {i+1}:")
            print(f"   Description: {description}")
            print(f"   Size: {size}")
            print(f"   Pairs: {pairs}")
            print(f"   Created: {created}")
            
            # Show symbols
            symbols = list(puzzle['pairs'].keys())
            print(f"   Symbols: {', '.join(map(str, symbols))}")
    
    def clear_collection(self):
        """Clear the current puzzle collection."""
        if not self.puzzles:
            print("\n📊 Collection is already empty.")
            return
        
        confirm = input(f"\n⚠️  Clear all {len(self.puzzles)} puzzles from collection? (y/n): ")
        if confirm.lower().startswith('y'):
            self.puzzles.clear()
            print("✅ Collection cleared.")
        else:
            print("❌ Clear cancelled.")
    
    def get_timestamp(self) -> str:
        """Get current timestamp as string."""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def solve_puzzles_from_file(filename: str = "my_puzzles.json"):
    """
    Standalone function to load and solve all puzzles from a file.

    Args:
        filename: Path to the JSON file containing puzzles
    """
    print(f"🚀 BATCH PUZZLE SOLVER")
    print("=" * 40)
    print(f"Loading puzzles from: {filename}")

    try:
        if not os.path.exists(filename):
            print(f"❌ File '{filename}' not found")
            return

        with open(filename, 'r') as f:
            loaded_data = json.load(f)

        # Handle different file formats
        if isinstance(loaded_data, list):
            puzzles = loaded_data
        elif isinstance(loaded_data, dict) and 'puzzles' in loaded_data:
            puzzles = loaded_data['puzzles']
        else:
            puzzles = [{'puzzle': loaded_data, 'description': 'Loaded puzzle'}]

        print(f"✅ Loaded {len(puzzles)} puzzles")
        print()

        # Solve all puzzles
        utility = PuzzleInputUtility()
        results = []

        for i, entry in enumerate(puzzles):
            puzzle = entry['puzzle']
            description = entry.get('description', f'Puzzle {i+1}')

            print(f"🎯 Solving {i+1}/{len(puzzles)}: {description}")
            print(f"   Size: {puzzle['rows']}×{puzzle['cols']}, Pairs: {len(puzzle['pairs'])}")

            solution = utility.solve_puzzle(puzzle, verbose=False)
            success = solution is not None
            results.append(success)

            if success:
                print("   ✅ SOLVED!")
            else:
                print("   ❌ Could not solve")
            print()

        # Summary
        solved = sum(results)
        total = len(results)
        print("=" * 40)
        print(f"📊 FINAL RESULTS")
        print(f"Solved: {solved}/{total} puzzles")
        print(f"Success rate: {solved/total*100:.1f}%")

        if solved == total:
            print("🎉 All puzzles solved successfully!")
        elif solved > 0:
            print("⚠️  Some puzzles couldn't be solved")
        else:
            print("❌ No puzzles could be solved")

    except json.JSONDecodeError:
        print(f"❌ Invalid JSON format in '{filename}'")
    except Exception as e:
        print(f"❌ Error: {e}")


def quick_add_puzzle():
    """Quick function to add a single puzzle interactively."""
    tool = InteractivePuzzleInput()
    tool.add_new_puzzle()

    # Auto-save if puzzles were added
    if tool.puzzles:
        save = input(f"\n💾 Save {len(tool.puzzles)} puzzle(s) to file? (y/n): ")
        if save.lower().startswith('y'):
            tool.save_puzzles_to_file()


def main():
    """Main entry point for the interactive tool."""
    import sys

    # Check for command line arguments
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == 'solve':
            filename = sys.argv[2] if len(sys.argv) > 2 else "my_puzzles.json"
            solve_puzzles_from_file(filename)
            return
        elif command == 'add':
            quick_add_puzzle()
            return
        elif command == 'help':
            print("🧩 FLOW FREE INTERACTIVE PUZZLE TOOL")
            print("=" * 40)
            print("Usage:")
            print("  python interactive_puzzle_input.py          # Interactive mode")
            print("  python interactive_puzzle_input.py solve    # Solve all puzzles in my_puzzles.json")
            print("  python interactive_puzzle_input.py solve <file>  # Solve puzzles in specific file")
            print("  python interactive_puzzle_input.py add      # Quick add single puzzle")
            print("  python interactive_puzzle_input.py help     # Show this help")
            return

    # Default: run interactive mode
    tool = InteractivePuzzleInput()

    try:
        tool.run()
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("Please report this issue if it persists.")


if __name__ == "__main__":
    main()
