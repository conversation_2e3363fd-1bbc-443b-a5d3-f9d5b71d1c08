#!/usr/bin/env python3
"""
Simple demonstration of the Flow Free Puzzle Input Utility.

This script shows the most common ways to use the utility to create
and solve puzzles from picture formats.
"""

from puzzle_input_utility import PuzzleInputUtility, create_puzzle_from_picture_guide
from puzzle_collection import Puz<PERSON><PERSON>ollection


def demo_basic_usage():
    """Demonstrate basic usage of the puzzle input utility."""
    print("🎯 BASIC PUZZLE INPUT UTILITY DEMO")
    print("="*50)
    
    utility = PuzzleInputUtility()
    
    # Example 1: Simple grid input (most common for picture translation)
    print("\n--- Example 1: Grid Input (Picture Translation) ---")
    print("Imagine you see a 4x4 puzzle with:")
    print("  - Red dots at corners (0,0) and (3,3)")
    print("  - Blue dots at (0,3) and (3,0)")
    print("  - Green dots at (1,1) and (2,2)")
    
    puzzle1 = utility.create_from_grid([
        ['R', '.', '.', 'B'],
        ['.', 'G', '.', '.'],
        ['.', '.', 'G', '.'],
        ['B', '.', '.', 'R']
    ])
    
    utility.print_puzzle_preview(puzzle1, "Picture Puzzle Translation")
    solution1 = utility.solve_puzzle(puzzle1, verbose=False)
    print(f"✅ Solution found: {'Yes' if solution1 else 'No'}")
    
    # Example 2: Coordinate input (precise control)
    print("\n--- Example 2: Coordinate Input (Precise Control) ---")
    puzzle2 = utility.create_from_coordinates(
        rows=3, cols=3,
        endpoints={
            1: [(0, 0), (2, 2)],  # Diagonal
            2: [(0, 2), (2, 0)]   # Other diagonal
        }
    )
    
    utility.print_puzzle_preview(puzzle2, "Coordinate-based Puzzle")
    solution2 = utility.solve_puzzle(puzzle2, verbose=False)
    print(f"✅ Solution found: {'Yes' if solution2 else 'No'}")
    
    # Example 3: String input (copy-paste friendly)
    print("\n--- Example 3: String Input (Copy-Paste Friendly) ---")
    puzzle_string = """
    1 . 2
    . . .
    2 . 1
    """
    
    puzzle3 = utility.create_from_string(puzzle_string)
    utility.print_puzzle_preview(puzzle3, "String-based Puzzle")
    solution3 = utility.solve_puzzle(puzzle3, verbose=False)
    print(f"✅ Solution found: {'Yes' if solution3 else 'No'}")


def demo_picture_workflow():
    """Demonstrate the complete workflow for translating a picture puzzle."""
    print("\n\n🖼️  PICTURE TRANSLATION WORKFLOW")
    print("="*50)
    
    print("""
SCENARIO: You have a picture of a 5x5 Flow Free puzzle with:
- Red circles at positions (0,0) and (4,4)
- Blue circles at positions (0,4) and (4,0)  
- Green circles at positions (2,1) and (2,3)

STEP 1: Identify the grid and endpoints
STEP 2: Choose input method (grid format recommended)
STEP 3: Create the puzzle
STEP 4: Validate and solve
""")
    
    utility = PuzzleInputUtility()
    
    # Step 2-3: Create the puzzle using grid format
    print("Creating puzzle from picture description...")
    puzzle = utility.create_from_grid([
        ['R', '.', '.', '.', 'B'],
        ['.', '.', '.', '.', '.'],
        ['.', 'G', '.', 'G', '.'],
        ['.', '.', '.', '.', '.'],
        ['B', '.', '.', '.', 'R']
    ])
    
    # Step 4: Validate and solve
    print("\nValidating puzzle...")
    is_valid, errors = utility.validate_puzzle(puzzle)
    if is_valid:
        print("✅ Puzzle is valid!")
        utility.print_puzzle_preview(puzzle, "Picture Puzzle")
        
        print("Solving puzzle...")
        solution = utility.solve_puzzle(puzzle, verbose=True)
        
        if solution:
            print("🎉 Successfully translated and solved picture puzzle!")
        else:
            print("❌ Puzzle couldn't be solved (may be too difficult)")
    else:
        print("❌ Puzzle validation failed:")
        for error in errors:
            print(f"  - {error}")


def demo_collection_usage():
    """Demonstrate using the pre-made puzzle collection."""
    print("\n\n📚 PUZZLE COLLECTION DEMO")
    print("="*50)
    
    try:
        collection = PuzzleCollection()
        
        print("Available puzzle categories:")
        easy_puzzles = collection.get_puzzles_by_difficulty("easy")
        medium_puzzles = collection.get_puzzles_by_difficulty("medium")
        
        print(f"  📗 Easy puzzles: {len(easy_puzzles)}")
        print(f"  📙 Medium puzzles: {len(medium_puzzles)}")
        
        # Try solving a simple puzzle
        print("\nTrying an easy puzzle...")
        solution = collection.solve_puzzle("simple_1x3", verbose=True)
        
        if solution:
            print("✅ Easy puzzle solved!")
        else:
            print("❌ Couldn't solve easy puzzle")
            
    except Exception as e:
        print(f"❌ Error with puzzle collection: {e}")
        print("This might be due to puzzle validation - some puzzles may be too complex for the current solver")


def demo_batch_processing():
    """Demonstrate batch processing of multiple puzzles."""
    print("\n\n⚡ BATCH PROCESSING DEMO")
    print("="*50)
    
    from puzzle_input_utility import BatchPuzzleProcessor
    
    # Create several simple puzzles
    puzzle_grids = [
        # Simple 2x2
        [
            ['1', '2'],
            ['2', '1']
        ],
        # Simple 1x3
        [
            ['1', '.', '1']
        ],
        # 3x3 with two pairs
        [
            ['1', '.', '2'],
            ['.', '.', '.'],
            ['2', '.', '1']
        ]
    ]
    
    processor = BatchPuzzleProcessor()
    print(f"Processing {len(puzzle_grids)} puzzles...")
    
    results = processor.create_puzzles_from_grids(puzzle_grids, solve=True)
    processor.print_batch_summary()


def main():
    """Run all demonstrations."""
    print("🚀 FLOW FREE PUZZLE INPUT UTILITY DEMONSTRATIONS")
    print("="*80)
    
    # Show the translation guide first
    print("\n📖 First, here's the translation guide:")
    create_puzzle_from_picture_guide()
    
    # Run demonstrations
    demo_basic_usage()
    demo_picture_workflow()
    demo_collection_usage()
    demo_batch_processing()
    
    print("\n" + "="*80)
    print("🎯 SUMMARY")
    print("="*80)
    print("""
The Flow Free Puzzle Input Utility provides three main ways to create puzzles:

1. 🎨 GRID FORMAT (Best for picture translation):
   utility.create_from_grid([
       ['1', '.', '2'],
       ['.', '.', '.'],
       ['2', '.', '1']
   ])

2. 📍 COORDINATE FORMAT (Best for precise input):
   utility.create_from_coordinates(
       rows=3, cols=3,
       endpoints={1: [(0,0), (2,2)], 2: [(0,2), (2,0)]}
   )

3. 📝 STRING FORMAT (Best for copy-paste):
   utility.create_from_string('''
   1 . 2
   . . .
   2 . 1
   ''')

WORKFLOW FOR PICTURE PUZZLES:
1. Count grid size (rows × columns)
2. Identify endpoint positions for each color/symbol
3. Use grid format to create puzzle
4. Validate and solve

ADDITIONAL FEATURES:
- Pre-made puzzle collection with various difficulties
- Batch processing for multiple puzzles
- Validation and error checking
- Export/import functionality
- Quality metrics and solving statistics

Happy puzzle solving! 🧩
""")


if __name__ == "__main__":
    main()
