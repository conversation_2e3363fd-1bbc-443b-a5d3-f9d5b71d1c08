# Interactive Flow Free Puzzle Input Tool

A command-line interface for entering Flow Free puzzles directly from the terminal, managing puzzle collections, and batch solving. Perfect for quickly inputting puzzles from pictures or other sources.

## 🚀 Quick Start

```bash
# Run the interactive tool
python interactive_puzzle_input.py

# Quick add a single puzzle
python interactive_puzzle_input.py add

# Solve all puzzles in a file
python interactive_puzzle_input.py solve my_puzzles.json

# Show help
python interactive_puzzle_input.py help
```

## 📋 Main Menu Options

When you run the interactive tool, you'll see:

```
📋 MAIN MENU:
1. 📝 Add new puzzle
2. 📁 Load puzzles from file
3. 🚀 Solve all puzzles in current collection
4. 💾 Save current collection to file
5. 📊 View current collection
6. 🔄 Clear current collection
7. ❌ Exit
```

## 📝 Adding New Puzzles

### Step-by-Step Process:

1. **Enter Grid Size**
   - Square grids: `5` (for 5×5)
   - Rectangular grids: `3x4` (for 3 rows × 4 columns)

2. **Input Grid Data**
   - Use numbers (1,2,3...) or letters (A,B,C...) for endpoints
   - Use `.` or space for empty cells
   - Each symbol must appear exactly twice
   - Enter one row at a time

3. **Example Input Session**
   ```
   Grid size: 4
   Row 1/4: 1 . . 2
   Row 2/4: . . . .
   Row 3/4: . . . .
   Row 4/4: 2 . . 1
   ```

4. **Validation & Description**
   - Tool automatically validates the puzzle
   - Add optional description
   - Choose to solve immediately or save for later

### Input Formats Supported:

- **Spaced format**: `1 . . 2`
- **Compact format**: `1..2`
- **Mixed symbols**: `A . B`, `1 . 2`, `R . B` (for Red/Blue)

## 📁 File Management

### Saving Puzzles
- Saves to JSON format with metadata
- Includes description, creation timestamp
- Default filename: `my_puzzles.json`
- Overwrite protection

### Loading Puzzles
- Supports multiple JSON formats
- Automatic coordinate conversion
- Shows summary of loaded puzzles
- Adds to current collection

### File Format
```json
[
  {
    "puzzle": {
      "rows": 4,
      "cols": 4,
      "pairs": {
        "1": [[0, 0], [3, 3]],
        "2": [[0, 3], [3, 0]]
      }
    },
    "description": "4x4 diagonal cross",
    "created": "2024-01-01 12:00:00"
  }
]
```

## 🚀 Batch Solving

### Solve All Puzzles in Collection
- Processes all puzzles in current memory
- Shows progress for each puzzle
- Detailed statistics and success rate

### Solve from File
```bash
# Solve default file
python interactive_puzzle_input.py solve

# Solve specific file
python interactive_puzzle_input.py solve puzzles.json
```

## 🎯 Picture Translation Workflow

### From Picture to Puzzle:

1. **Analyze the Picture**
   - Count grid size (e.g., 5×5)
   - Identify colored dots/circles/numbers
   - Note positions of each pair

2. **Run the Tool**
   ```bash
   python interactive_puzzle_input.py add
   ```

3. **Enter Data**
   ```
   Grid size: 5
   Row 1/5: R . . . B    # Red and Blue endpoints
   Row 2/5: . . . . .
   Row 3/5: . G . G .    # Green endpoints
   Row 4/5: . . . . .
   Row 5/5: B . . . R
   ```

4. **Validate & Solve**
   - Tool validates automatically
   - Option to solve immediately
   - Save to collection for later

## 🎮 Command Line Options

### Interactive Mode (Default)
```bash
python interactive_puzzle_input.py
```
Full menu with all options.

### Quick Add
```bash
python interactive_puzzle_input.py add
```
Directly add a single puzzle, then exit.

### Batch Solve
```bash
python interactive_puzzle_input.py solve [filename]
```
Load and solve all puzzles from file.

### Help
```bash
python interactive_puzzle_input.py help
```
Show usage information.

## 📊 Features

### ✅ Validation
- Ensures each symbol appears exactly twice
- Checks grid dimensions
- Validates coordinate bounds
- Provides detailed error messages

### 🎯 User-Friendly
- Clear prompts and instructions
- Visual grid display
- Progress indicators
- Confirmation dialogs

### 💾 Persistent Storage
- JSON file format
- Metadata preservation
- Multiple file support
- Backup protection

### 🚀 Batch Processing
- Multiple puzzle solving
- Progress tracking
- Success statistics
- Error handling

## 🔧 Tips for Success

### Grid Input Tips:
- **Count carefully**: Double-check grid dimensions
- **Consistent symbols**: Use same symbol for both endpoints
- **Clear separation**: Use spaces or dots for empty cells
- **Verify pairs**: Each symbol should appear exactly twice

### Common Formats:
```
# Numbers
1 . . 2
. . . .
2 . . 1

# Letters  
A . . B
. . . .
B . . A

# Colors (for picture translation)
R . . B    # Red, Blue
. . . .
B . . R
```

### Error Prevention:
- Start with small puzzles (3×3) to learn the format
- Use the preview to verify before solving
- Save frequently to avoid losing work
- Test with simple patterns first

## 📈 Example Session

```bash
$ python interactive_puzzle_input.py

🧩 FLOW FREE INTERACTIVE PUZZLE INPUT TOOL
==================================================

📋 MAIN MENU:
1. 📝 Add new puzzle
2. 📁 Load puzzles from file
3. 🚀 Solve all puzzles in current collection
4. 💾 Save current collection to file
5. 📊 View current collection
6. 🔄 Clear current collection
7. ❌ Exit

Select option (1-7): 1

📝 ADD NEW PUZZLE
------------------------------

🔢 Enter grid size:
Grid size: 3

📐 Creating 3×3 grid
💡 Tips:
  - Use numbers (1,2,3...) or letters (A,B,C...) for endpoints
  - Use '.' or space for empty cells
  - Each symbol must appear exactly twice

Row 1/3: 1 . 2
Row 2/3: . . .
Row 3/3: 2 . 1

📋 Your 3×3 puzzle:
   ─────────────
1 │ 1 │ · │ 2 │
2 │ · │ · │ · │
3 │ 2 │ · │ 1 │
   ─────────────

✅ Puzzle is valid!

📝 Enter description: Diagonal cross pattern

✅ Puzzle added to collection! (Total: 1)

🚀 Solve this puzzle now? (y/n): y
```

## 🎉 Benefits

- **Fast Input**: Quickly enter puzzles from any source
- **Validation**: Automatic error checking
- **Batch Processing**: Handle multiple puzzles efficiently
- **Persistent Storage**: Save and reload puzzle collections
- **Integration**: Works seamlessly with existing solver
- **User-Friendly**: Clear interface and helpful prompts

Perfect for puzzle enthusiasts who want to quickly digitize and solve Flow Free puzzles from pictures, books, or mobile games!
