#!/usr/bin/env python3
"""
Custom Flow Free puzzle test template.
Modify this file to test your own puzzles.
"""

from main import FlowFreeSolver

def test_custom_puzzle():
    """Test a custom puzzle - modify this function for your own tests."""
    print("=== Custom Puzzle Test ===")
    
    # Define your puzzle here
    # Format: symbol: ((start_row, start_col), (end_row, end_col))
    pairs = {
        1: ((0, 0), (2, 2)),  # Example: connect (0,0) to (2,2)
        2: ((0, 2), (2, 0)),  # Example: connect (0,2) to (2,0)
    }
    
    # Set the grid size (rows, cols)
    rows, cols = 3, 3
    
    # Create and display the puzzle
    solver = FlowFreeSolver(rows, cols, pairs)
    solver.print_current_grid("Custom Puzzle")
    
    # Solve the puzzle
    print("🚀 Starting solver...")
    if solver.solve():
        solver.print_solution()
        return True
    else:
        print("❌ No solution found")
        solver.print_current_grid("Final State (No Solution)")
        return False

def test_another_custom():
    """Add more custom test functions here."""
    print("\n=== Another Custom Test ===")
    
    # Example: 2x2 puzzle
    pairs = {
        1: ((0, 0), (1, 1)),
        2: ((0, 1), (1, 0))
    }
    
    solver = FlowFreeSolver(2, 2, pairs)
    solver.print_current_grid("2x2 Cross Pattern")
    
    print("🚀 Starting solver...")
    if solver.solve():
        solver.print_solution()
        return True
    else:
        print("❌ No solution found")
        solver.print_current_grid("Final State (No Solution)")
        return False

if __name__ == "__main__":
    print("Running custom puzzle tests...\n")
    
    # Run your tests
    test1_success = test_custom_puzzle()
    test2_success = test_another_custom()
    
    # Summary
    print("\n" + "="*50)
    print("Test Results:")
    print(f"Custom Test 1: {'✅ PASSED' if test1_success else '❌ FAILED'}")
    print(f"Custom Test 2: {'✅ PASSED' if test2_success else '❌ FAILED'}")
    
    if test1_success and test2_success:
        print("\n🎉 All tests passed!")
    else:
        print("\n⚠️  Some tests failed.")
