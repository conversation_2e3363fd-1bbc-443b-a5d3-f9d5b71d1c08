#!/usr/bin/env python3
"""
Test the enhanced Flow Free solver with multiple paths and reachability checking.
This test demonstrates the new features:
1. Finding all valid paths for each symbol
2. Reachability checking to prevent unreachable areas
3. Path quality metrics (currently all 0)
4. Enhanced backtracking with multiple path combinations
"""

from main import FlowFreeSolver

def test_enhanced_solver_simple():
    """Test the enhanced solver on a simple puzzle."""
    print("=== Enhanced Solver Test - Simple 3x3 Puzzle ===")
    
    # Create a 3x3 puzzle that should have multiple valid paths
    pairs = {
        1: ((0, 0), (2, 2)),  # Diagonal connection
        2: ((0, 2), (2, 0))   # Other diagonal
    }
    
    solver = FlowFreeSolver(3, 3, pairs)
    solver.print_current_grid("Enhanced Solver Test - 3x3")
    
    print("🚀 Starting enhanced solver...")
    if solver.solve():
        solver.print_solution()
        solver.print_all_paths_info()
        return True
    else:
        print("❌ No solution found")
        solver.print_current_grid("Final State (No Solution)")
        solver.print_all_paths_info()
        return False

def test_reachability_checking():
    """Test a puzzle that should fail reachability checking."""
    print("\n=== Reachability Checking Test ===")
    
    # Create a puzzle that might create unreachable areas
    pairs = {
        1: ((0, 0), (0, 2)),  # Top row connection
        2: ((1, 0), (1, 2)),  # Middle row connection  
        3: ((2, 0), (2, 2))   # Bottom row connection
    }
    
    solver = FlowFreeSolver(3, 3, pairs)
    solver.print_current_grid("Reachability Test - 3x3")
    
    print("🚀 Starting solver with reachability checking...")
    if solver.solve():
        solver.print_solution()
        solver.print_all_paths_info()
        return True
    else:
        print("❌ No solution found (likely due to reachability issues)")
        solver.print_current_grid("Final State (No Solution)")
        solver.print_all_paths_info()
        return False

def test_multiple_paths_discovery():
    """Test discovery of multiple paths for symbols."""
    print("\n=== Multiple Paths Discovery Test ===")
    
    # Create a simple puzzle where symbols should have multiple path options
    pairs = {
        1: ((0, 0), (1, 1))  # Simple connection with multiple possible routes
    }
    
    solver = FlowFreeSolver(2, 2, pairs)
    solver.print_current_grid("Multiple Paths Test - 2x2")
    
    # Manually test path discovery for symbol 1
    symbol = 1
    start, end = solver.pairs[symbol]
    paths = solver.find_all_paths(symbol, start, end)
    
    print(f"\n📊 Found {len(paths)} paths for symbol {symbol}:")
    for i, path_info in enumerate(paths):
        path = path_info['path']
        quality = path_info['quality']
        print(f"  Path {i+1}: {path} (quality: {quality})")
    
    print("\n🚀 Now solving with enhanced solver...")
    if solver.solve():
        solver.print_solution()
        return True
    else:
        print("❌ No solution found")
        return False

def test_quality_metrics():
    """Test that quality metrics are being calculated (even if they're all 0)."""
    print("\n=== Quality Metrics Test ===")
    
    pairs = {
        1: ((0, 0), (2, 0)),  # Vertical connection
        2: ((0, 1), (2, 1))   # Another vertical connection
    }
    
    solver = FlowFreeSolver(3, 2, pairs)
    solver.print_current_grid("Quality Metrics Test - 3x2")
    
    print("🚀 Testing quality metric calculation...")
    
    # Test quality calculation for a sample path
    sample_path = [(0, 0), (1, 0), (2, 0)]
    quality = solver.calculate_path_quality(sample_path, 1)
    print(f"Sample path {sample_path} has quality: {quality}")
    
    # Now solve and show all path qualities
    if solver.solve():
        solver.print_solution()
        solver.print_all_paths_info()
        return True
    else:
        print("❌ No solution found")
        solver.print_all_paths_info()
        return False

if __name__ == "__main__":
    print("🧪 Testing Enhanced Flow Free Solver")
    print("=" * 50)
    
    tests = [
        ("Simple Enhanced Solver", test_enhanced_solver_simple),
        ("Reachability Checking", test_reachability_checking),
        ("Multiple Paths Discovery", test_multiple_paths_discovery),
        ("Quality Metrics", test_quality_metrics)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"✅ {test_name}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"  {test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed - check the output above for details")
